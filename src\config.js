const path = require('path');
const fs = require('fs');

class ConfigManager {
    constructor() {
        this.configPath = path.join(__dirname, '..', '.env');
        this.config = {};
        this.loadConfig();
    }

    loadConfig() {
        try {
            // Load environment variables from .env file
            if (fs.existsSync(this.configPath)) {
                const envContent = fs.readFileSync(this.configPath, 'utf8');
                const lines = envContent.split('\n');
                
                lines.forEach(line => {
                    line = line.trim();
                    if (line && !line.startsWith('#') && line.includes('=')) {
                        const [key, ...valueParts] = line.split('=');
                        const value = valueParts.join('=').trim();
                        // Store trimmed key and value, and remove any quotes
                        const cleanKey = key.trim();
                        const cleanValue = value.replace(/^["']|["']$/g, '').trim();
                        this.config[cleanKey] = cleanValue;

                        // Debug log for troubleshooting
                        if (cleanKey === 'RESEND_API_KEY' || cleanKey === 'DEFAULT_BUSINESS_EMAIL') {
                            console.log(`📧 Config loaded: ${cleanKey} = ${cleanValue ? '***SET***' : 'EMPTY'}`);
                        }
                    }
                });
            }
            
            console.log('📧 Configuration loaded successfully');
        } catch (error) {
            console.error('Error loading configuration:', error);
        }
    }

    saveConfig() {
        try {
            const configLines = [
                '# Rainbow Station POS - Environment Configuration',
                '# Add your actual Resend API key here',
                '',
                '# Resend Email Service Configuration',
                `RESEND_API_KEY=${this.config.RESEND_API_KEY || ''}`,
                '',
                '# Default Email Settings',
                `DEFAULT_BUSINESS_EMAIL=${this.config.DEFAULT_BUSINESS_EMAIL || ''}`,
                `DEFAULT_SENDER_NAME=${this.config.DEFAULT_SENDER_NAME || 'Rainbow Station POS'}`,
                '',
                '# Email Configuration',
                `AUTO_SEND_DAILY_REPORTS=${this.config.AUTO_SEND_DAILY_REPORTS || 'false'}`,
                `DAILY_REPORT_TIME=${this.config.DAILY_REPORT_TIME || '18:00'}`,
                '',
                '# Application Settings',
                `NODE_ENV=${this.config.NODE_ENV || 'production'}`,
                `DEBUG_MODE=${this.config.DEBUG_MODE || 'false'}`
            ];

            fs.writeFileSync(this.configPath, configLines.join('\n'), 'utf8');
            console.log('📧 Configuration saved successfully');
            return true;
        } catch (error) {
            console.error('Error saving configuration:', error);
            return false;
        }
    }

    get(key, defaultValue = null) {
        return this.config[key] || defaultValue;
    }

    set(key, value) {
        this.config[key] = value;
        return this.saveConfig();
    }

    setMultiple(keyValuePairs) {
        Object.keys(keyValuePairs).forEach(key => {
            this.config[key] = keyValuePairs[key];
        });
        return this.saveConfig();
    }

    // Email specific getters
    getResendApiKey() {
        return this.get('RESEND_API_KEY');
    }

    getDefaultBusinessEmail() {
        return this.get('DEFAULT_BUSINESS_EMAIL');
    }

    getDefaultSenderName() {
        return this.get('DEFAULT_SENDER_NAME', 'Rainbow Station POS');
    }

    getAutoSendDailyReports() {
        return this.get('AUTO_SEND_DAILY_REPORTS', 'false') === 'true';
    }

    getDailyReportTime() {
        return this.get('DAILY_REPORT_TIME', '18:00');
    }

    // Email specific setters
    setResendApiKey(apiKey) {
        return this.set('RESEND_API_KEY', apiKey);
    }

    setDefaultBusinessEmail(email) {
        return this.set('DEFAULT_BUSINESS_EMAIL', email);
    }

    setDefaultSenderName(name) {
        return this.set('DEFAULT_SENDER_NAME', name);
    }

    setAutoSendDailyReports(enabled) {
        return this.set('AUTO_SEND_DAILY_REPORTS', enabled ? 'true' : 'false');
    }

    setDailyReportTime(time) {
        return this.set('DAILY_REPORT_TIME', time);
    }

    // Validation methods
    isEmailConfigured() {
        const apiKey = this.getResendApiKey();
        const businessEmail = this.getDefaultBusinessEmail();
        return !!(apiKey && apiKey.trim() && businessEmail && businessEmail.trim());
    }

    validateEmailConfig() {
        const errors = [];
        
        const apiKey = this.getResendApiKey();
        if (!apiKey || !apiKey.trim()) {
            errors.push('Resend API key is required');
        } else if (!apiKey.startsWith('re_')) {
            errors.push('Invalid Resend API key format (should start with "re_")');
        }

        const businessEmail = this.getDefaultBusinessEmail();
        if (!businessEmail || !businessEmail.trim()) {
            errors.push('Business email is required');
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(businessEmail)) {
            errors.push('Invalid business email format');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    // Get all email configuration
    getEmailConfig() {
        return {
            apiKey: this.getResendApiKey(),
            businessEmail: this.getDefaultBusinessEmail(),
            senderName: this.getDefaultSenderName(),
            autoSendDaily: this.getAutoSendDailyReports(),
            dailyReportTime: this.getDailyReportTime()
        };
    }

    // Set all email configuration
    setEmailConfig(config) {
        const updates = {};
        
        if (config.apiKey !== undefined) {
            updates.RESEND_API_KEY = config.apiKey;
        }
        if (config.businessEmail !== undefined) {
            updates.DEFAULT_BUSINESS_EMAIL = config.businessEmail;
        }
        if (config.senderName !== undefined) {
            updates.DEFAULT_SENDER_NAME = config.senderName;
        }
        if (config.autoSendDaily !== undefined) {
            updates.AUTO_SEND_DAILY_REPORTS = config.autoSendDaily ? 'true' : 'false';
        }
        if (config.dailyReportTime !== undefined) {
            updates.DAILY_REPORT_TIME = config.dailyReportTime;
        }

        return this.setMultiple(updates);
    }

    // Debug method
    debugConfig() {
        console.log('📧 Current Email Configuration:');
        console.log('- API Key:', this.getResendApiKey() ? '***configured***' : 'NOT SET');
        console.log('- Business Email:', this.getDefaultBusinessEmail() || 'NOT SET');
        console.log('- Sender Name:', this.getDefaultSenderName());
        console.log('- Auto Send Daily:', this.getAutoSendDailyReports());
        console.log('- Daily Report Time:', this.getDailyReportTime());
        console.log('- Is Configured:', this.isEmailConfigured());
    }
}

module.exports = ConfigManager;
