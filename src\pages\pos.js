const { ipc<PERSON>enderer } = require('electron');

// Email Configuration Popup Component for POS
class PosEmailConfigPopup {
    constructor() {
        this.isVisible = false;
        this.reportType = null; // 'daily' or 'shift'
        this.shiftId = null;
        this.createPopupHTML();
        this.attachEventListeners();
    }

    createPopupHTML() {
        const popupHTML = `
            <div id="pos-email-config-overlay" class="pos-email-popup-overlay" style="display: none;">
                <div class="pos-email-popup-container">
                    <div class="pos-email-popup-header">
                        <h3 id="pos-email-popup-title">Send Report via Email</h3>
                        <button id="pos-email-popup-close" class="pos-email-popup-close">&times;</button>
                    </div>

                    <div class="pos-email-popup-content">
                        <div class="pos-email-form-group">
                            <label for="pos-recipient-email">Business Owner Email *</label>
                            <input type="email" id="pos-recipient-email" placeholder="Enter business owner email address" required>
                        </div>

                        <div class="pos-email-form-group">
                            <label for="pos-resend-api-key">Resend API Key *</label>
                            <input type="password" id="pos-resend-api-key" placeholder="Enter your Resend API key">
                            <small class="pos-email-help-text">
                                Don't have an API key? <a href="#" onclick="openResendSignupFromPos()">Sign up at resend.com</a>
                            </small>
                        </div>

                        <div class="pos-email-form-group">
                            <label for="pos-sender-name">Sender Name</label>
                            <input type="text" id="pos-sender-name" placeholder="Rainbow Station POS" value="Rainbow Station POS">
                        </div>

                        <div class="pos-email-form-group">
                            <label>
                                <input type="checkbox" id="pos-save-config"> Save configuration for future use
                            </label>
                        </div>

                        <div class="pos-email-form-group">
                            <button id="pos-test-email-btn" class="pos-email-btn pos-email-btn-secondary">Test Email Configuration</button>
                        </div>
                    </div>

                    <div class="pos-email-popup-footer">
                        <button id="pos-cancel-email-btn" class="pos-email-btn pos-email-btn-cancel">Cancel</button>
                        <button id="pos-send-email-btn" class="pos-email-btn pos-email-btn-primary">Send Report</button>
                    </div>

                    <div id="pos-email-status" class="pos-email-status" style="display: none;"></div>
                </div>
            </div>
        `;

        // Add CSS styles for POS email popup
        const styles = `
            <style>
                .pos-email-popup-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 20000;
                }

                .pos-email-popup-container {
                    background: white;
                    border-radius: 12px;
                    width: 500px;
                    max-width: 90vw;
                    max-height: 90vh;
                    overflow-y: auto;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                    border: 2px solid #3b82f6;
                }

                .pos-email-popup-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 24px;
                    border-bottom: 2px solid #e5e7eb;
                    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
                    color: white;
                    border-radius: 12px 12px 0 0;
                }

                .pos-email-popup-header h3 {
                    margin: 0;
                    font-size: 20px;
                    font-weight: 700;
                }

                .pos-email-popup-close {
                    background: none;
                    border: none;
                    font-size: 28px;
                    cursor: pointer;
                    color: white;
                    padding: 0;
                    width: 35px;
                    height: 35px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                    transition: background-color 0.2s;
                }

                .pos-email-popup-close:hover {
                    background-color: rgba(255, 255, 255, 0.2);
                }

                .pos-email-popup-content {
                    padding: 24px;
                }

                .pos-email-form-group {
                    margin-bottom: 20px;
                }

                .pos-email-form-group label {
                    display: block;
                    margin-bottom: 8px;
                    font-weight: 600;
                    color: #374151;
                    font-size: 15px;
                }

                .pos-email-form-group input[type="email"],
                .pos-email-form-group input[type="password"],
                .pos-email-form-group input[type="text"] {
                    width: 100%;
                    padding: 12px 16px;
                    border: 2px solid #d1d5db;
                    border-radius: 8px;
                    font-size: 15px;
                    box-sizing: border-box;
                    transition: border-color 0.2s;
                }

                .pos-email-form-group input:focus {
                    outline: none;
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
                }

                .pos-email-help-text {
                    display: block;
                    margin-top: 6px;
                    font-size: 13px;
                    color: #6b7280;
                }

                .pos-email-help-text a {
                    color: #3b82f6;
                    text-decoration: none;
                    font-weight: 500;
                }

                .pos-email-help-text a:hover {
                    text-decoration: underline;
                }

                .pos-email-popup-footer {
                    display: flex;
                    justify-content: flex-end;
                    gap: 16px;
                    padding: 24px;
                    border-top: 2px solid #e5e7eb;
                    background-color: #f8f9fa;
                    border-radius: 0 0 12px 12px;
                }

                .pos-email-btn {
                    padding: 12px 24px;
                    border: none;
                    border-radius: 8px;
                    font-size: 15px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s;
                }

                .pos-email-btn-primary {
                    background-color: #3b82f6;
                    color: white;
                }

                .pos-email-btn-primary:hover {
                    background-color: #2563eb;
                    transform: translateY(-1px);
                }

                .pos-email-btn-secondary {
                    background-color: #6b7280;
                    color: white;
                    width: 100%;
                }

                .pos-email-btn-secondary:hover {
                    background-color: #4b5563;
                }

                .pos-email-btn-cancel {
                    background-color: #e5e7eb;
                    color: #374151;
                }

                .pos-email-btn-cancel:hover {
                    background-color: #d1d5db;
                }

                .pos-email-status {
                    margin: 20px 24px;
                    padding: 16px;
                    border-radius: 8px;
                    font-size: 15px;
                    font-weight: 500;
                }

                .pos-email-status.success {
                    background-color: #ecfdf5;
                    color: #065f46;
                    border: 2px solid #d1fae5;
                }

                .pos-email-status.error {
                    background-color: #fef2f2;
                    color: #dc2626;
                    border: 2px solid #fecaca;
                }

                .pos-email-status.loading {
                    background-color: #eff6ff;
                    color: #1e40af;
                    border: 2px solid #dbeafe;
                }
            </style>
        `;

        // Add to document
        if (!document.getElementById('pos-email-popup-styles')) {
            const styleElement = document.createElement('div');
            styleElement.id = 'pos-email-popup-styles';
            styleElement.innerHTML = styles;
            document.head.appendChild(styleElement);
        }

        if (!document.getElementById('pos-email-config-overlay')) {
            document.body.insertAdjacentHTML('beforeend', popupHTML);
        }
    }

    attachEventListeners() {
        // Close popup events
        const closeBtn = document.getElementById('pos-email-popup-close');
        const cancelBtn = document.getElementById('pos-cancel-email-btn');
        const overlay = document.getElementById('pos-email-config-overlay');

        if (closeBtn) closeBtn.addEventListener('click', () => this.hide());
        if (cancelBtn) cancelBtn.addEventListener('click', () => this.hide());
        if (overlay) {
            overlay.addEventListener('click', (e) => {
                if (e.target.id === 'pos-email-config-overlay') {
                    this.hide();
                }
            });
        }

        // Test email configuration
        const testBtn = document.getElementById('pos-test-email-btn');
        if (testBtn) testBtn.addEventListener('click', () => this.testEmailConfiguration());

        // Send email
        const sendBtn = document.getElementById('pos-send-email-btn');
        if (sendBtn) sendBtn.addEventListener('click', () => this.sendReport());
    }

    show(reportType, shiftId = null) {
        this.reportType = reportType;
        this.shiftId = shiftId;

        // Update popup title based on report type
        const title = reportType === 'daily' ? 'Send Daily Sales Report' : 'Send Shift Performance Report';
        const titleEl = document.getElementById('pos-email-popup-title');
        if (titleEl) titleEl.textContent = title;

        // Show popup
        const overlay = document.getElementById('pos-email-config-overlay');
        if (overlay) {
            overlay.style.display = 'flex';
            this.isVisible = true;

            // Focus on email input
            setTimeout(() => {
                const emailInput = document.getElementById('pos-recipient-email');
                if (emailInput) emailInput.focus();
            }, 100);
        }
    }

    hide() {
        const overlay = document.getElementById('pos-email-config-overlay');
        if (overlay) {
            overlay.style.display = 'none';
            this.isVisible = false;
            this.hideStatus();
        }
    }

    showStatus(message, type) {
        const statusEl = document.getElementById('pos-email-status');
        if (statusEl) {
            statusEl.textContent = message;
            statusEl.className = `pos-email-status ${type}`;
            statusEl.style.display = 'block';
        }
    }

    hideStatus() {
        const statusEl = document.getElementById('pos-email-status');
        if (statusEl) {
            statusEl.style.display = 'none';
        }
    }

    async testEmailConfiguration() {
        const recipientEmail = document.getElementById('pos-recipient-email').value.trim();
        const apiKey = document.getElementById('pos-resend-api-key').value.trim();

        if (!recipientEmail || !apiKey) {
            this.showStatus('Please enter both email address and API key', 'error');
            return;
        }

        this.showStatus('Testing email configuration...', 'loading');

        try {
            // Set API key first
            await ipcRenderer.invoke('set-email-api-key', apiKey);

            // Test configuration
            const result = await ipcRenderer.invoke('test-email-configuration', recipientEmail);

            if (result.success) {
                this.showStatus('✅ Email configuration test successful!', 'success');
            } else {
                this.showStatus(`❌ Test failed: ${result.message}`, 'error');
            }
        } catch (error) {
            this.showStatus(`❌ Test failed: ${error.message}`, 'error');
        }
    }

    async sendReport() {
        const recipientEmail = document.getElementById('pos-recipient-email').value.trim();
        const apiKey = document.getElementById('pos-resend-api-key').value.trim();
        const saveConfig = document.getElementById('pos-save-config').checked;

        if (!recipientEmail || !apiKey) {
            this.showStatus('Please enter both email address and API key', 'error');
            return;
        }

        this.showStatus('Sending report...', 'loading');

        try {
            // Set API key
            await ipcRenderer.invoke('set-email-api-key', apiKey);

            // Send appropriate report
            let result;
            if (this.reportType === 'daily') {
                result = await ipcRenderer.invoke('send-daily-report-email', recipientEmail);
            } else if (this.reportType === 'shift') {
                result = await ipcRenderer.invoke('send-shift-report-email', recipientEmail, this.shiftId);
            }

            if (result.success) {
                this.showStatus('✅ Report sent successfully!', 'success');
                setTimeout(() => {
                    this.hide();
                }, 2000);
            } else {
                this.showStatus(`❌ Failed to send report: ${result.message}`, 'error');
            }
        } catch (error) {
            this.showStatus(`❌ Error: ${error.message}`, 'error');
        }
    }
}

// Global function to open Resend signup from POS
function openResendSignupFromPos() {
    require('electron').shell.openExternal('https://resend.com/signup');
}

// Initialize POS email popup when page loads
let posEmailConfigPopup = null;
document.addEventListener('DOMContentLoaded', function() {
    posEmailConfigPopup = new PosEmailConfigPopup();
});

// Import day.js for time handling (same as tickets)
const dayjs = require('dayjs');

// Global state
let cartItems = [];
let currentInput = "";
let selectedItemIndex = 0;
let specialDiscount = false;
let discountAmount = 0;
let isEditingPrice = false;

// Products data - loaded from database
let demoProducts = [];

// Shift management
let currentShift = null;
let shiftTimer = null;
let shiftUpdateInterval = null;

// Load products from database
async function loadProducts() {
    try {
        console.log('POS - Loading products from database...');

        // Log current user context for security validation
        if (currentUser) {
            console.log(`POS - ✅ SECURITY: Loading products for user: ${currentUser.username} (${currentUser.role})`);
            console.log(`POS - ✅ SECURITY: User location: ${currentUser.location_name} (ID: ${currentUser.location_id})`);
        } else {
            console.warn('POS - ⚠️ SECURITY: No current user context when loading products');
        }

        const result = await ipcRenderer.invoke('get-pos-products');

        if (result.success && result.products) {
            demoProducts = result.products.map(product => ({
                id: product.id.toString(),
                name: product.name || product.description,
                barcode: product.barcode,
                price: product.price || 0, // Use price from products table (purchase_price)
                category: product.category || 'Uncategorized',
                subcategory: product.subcategory || 'General',
                daily_item: product.daily_item || 0, // Include daily_item field
                image: product.image || product.image_path || `https://via.placeholder.com/200x200/4CAF50/FFFFFF?text=${encodeURIComponent((product.name || product.description).substring(0, 10))}`
            }));

            console.log(`POS - ✅ SECURITY: Successfully loaded ${demoProducts.length} location-filtered products for ${currentUser?.location_name || 'Unknown location'}`);

            // Log sample products for validation (first 3)
            if (demoProducts.length > 0) {
                console.log('POS - ✅ SECURITY: Sample products with pricing:', demoProducts.slice(0, 3).map(p => ({
                    name: p.name,
                    id: p.id,
                    barcode: p.barcode,
                    price: p.price
                })));
                console.log('POS - 🔍 PRICING: Using purchase_price from products table');
            }

            // Only refresh UI if modal elements exist (modal is open)
            if (document.getElementById('categories-list')) {
                populateCategories();
            }
            if (document.getElementById('products-grid')) {
                populateProductsGrid();
            }
        } else {
            console.error('POS - ❌ SECURITY: Error loading products:', result.message || 'Unknown error');
            console.error('POS - ❌ SECURITY: Full result:', result);
            // Keep empty array if no products found
            demoProducts = [];
        }
    } catch (error) {
        console.error('POS - ❌ SECURITY: Exception loading products:', error);
        demoProducts = [];
    }
}

// Current user management
let currentUser = null;
let userPermissions = [];

async function loadCurrentUser() {
    try {
        console.log('POS - Loading current user...');
        currentUser = await ipcRenderer.invoke('get-current-user');

        if (currentUser) {
            // Extract permissions safely
            userPermissions = currentUser.permissions || [];

            console.log('POS - User loaded successfully:', {
                username: currentUser.username,
                role: currentUser.role,
                permissionCount: userPermissions.length
            });

            // Log permissions for debugging
            if (userPermissions.length > 0) {
                console.log('POS - User permissions:', userPermissions.map(p => p.module_id));
            }

            updateOperatorInfo();
        } else {
            console.error('POS - No current user returned - this should not happen');
            // DO NOT set fallback admin - this compromises security
            currentUser = null;
            userPermissions = [];
            document.getElementById('current-operator').textContent = 'Error: No User';
        }
    } catch (error) {
        console.error('Error loading current user in POS:', error);
        // DO NOT set fallback admin - this compromises security
        currentUser = null;
        userPermissions = [];
        document.getElementById('current-operator').textContent = 'Error: User Load Failed';
    }
}

function updateOperatorInfo() {
    if (currentUser) {
        const operatorSpan = document.getElementById('current-operator');
        if (operatorSpan) {
            operatorSpan.textContent = currentUser.name || currentUser.username || 'Unknown';
        }

        // Update location name in header
        const locationNameElement = document.getElementById('location-name');
        if (locationNameElement) {
            if (currentUser.location_name) {
                locationNameElement.textContent = currentUser.location_name;
            } else {
                locationNameElement.textContent = 'Rainbow Station Inc.';
            }
        }

        // Update location security indicator
        updateLocationSecurityIndicator();
    }
}

function updateLocationSecurityIndicator() {
    const indicator = document.getElementById('location-access-status');
    const indicatorContainer = document.querySelector('.location-security-indicator');

    if (indicator && indicatorContainer) {
        if (currentUser && currentUser.location_id && currentUser.location_name) {
            indicator.textContent = `Secure (${currentUser.location_name})`;
            indicatorContainer.className = 'location-security-indicator';
        } else if (currentUser && !currentUser.location_id) {
            indicator.textContent = 'No Location Assigned';
            indicatorContainer.className = 'location-security-indicator error';
        } else {
            indicator.textContent = 'Not Authenticated';
            indicatorContainer.className = 'location-security-indicator error';
        }
    }
}

// Permission checking functions - RESTRICTIVE (deny by default)
function hasModuleAccess(moduleId) {
    try {
        // If no current user loaded, DENY access
        if (!currentUser) {
            console.log('POS - No current user loaded, DENYING access to', moduleId);
            return false;
        }

        // Admin has access to everything
        if (currentUser.role === 'Admin') {
            console.log('POS - Admin user, allowing access to', moduleId);
            return true;
        }

        // Check if user has permission for this module
        const hasAccess = userPermissions && userPermissions.some(perm => perm.module_id === moduleId);
        console.log(`POS - Access check for ${moduleId}: ${hasAccess} (user: ${currentUser.username}, role: ${currentUser.role})`);

        return hasAccess;
    } catch (error) {
        console.error('Error checking module access:', error);
        // Default to DENYING access if there's an error
        return false;
    }
}

function canAccessAdmin() {
    return hasModuleAccess('dashboard') || hasModuleAccess('master') || hasModuleAccess('reports') ||
           hasModuleAccess('transactions') || hasModuleAccess('wholesale') || hasModuleAccess('user-management');
}

function canAccessTheater() {
    return hasModuleAccess('theater');
}

// Apply access control restrictions to POS interface
function applyAccessControlRestrictions() {
    console.log('POS - Applying access control restrictions...');

    if (!currentUser) {
        console.warn('POS - No current user, cannot apply restrictions');
        return;
    }

    restrictAdminButton();
    restrictTheaterButton();

    console.log('POS - Access control restrictions applied');
}

function restrictAdminButton() {
    const adminBtn = document.querySelector('button[onclick="navigateToAdmin()"]');

    if (adminBtn) {
        if (!canAccessAdmin()) {
            console.log('POS - Restricting admin button access');
            adminBtn.style.opacity = '0.5';
            adminBtn.style.cursor = 'not-allowed';
            adminBtn.style.pointerEvents = 'none';
            adminBtn.title = 'Access Denied: You do not have permission to access Admin Panel';

            // Replace onclick with access denied message
            adminBtn.removeAttribute('onclick');
            adminBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                showAccessDeniedMessage('Admin Panel');
            });
        } else {
            console.log('POS - Admin button access allowed');
        }
    }
}

function restrictTheaterButton() {
    const theaterBtn = document.querySelector('button[onclick="navigateToTheater()"]');

    if (theaterBtn) {
        if (!canAccessTheater()) {
            console.log('POS - Restricting theater button access');
            theaterBtn.style.opacity = '0.5';
            theaterBtn.style.cursor = 'not-allowed';
            theaterBtn.style.pointerEvents = 'none';
            theaterBtn.title = 'Access Denied: You do not have permission to access Theater Management';

            // Replace onclick with access denied message
            theaterBtn.removeAttribute('onclick');
            theaterBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                showAccessDeniedMessage('Theater Management');
            });
        } else {
            console.log('POS - Theater button access allowed');
        }
    }
}

function showAccessDeniedMessage(moduleName) {
    alert(`Access Denied: You do not have permission to access ${moduleName}.`);
}

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    console.log('POS - DOM Content Loaded, initializing...');

    updateTime();
    setInterval(updateTime, 1000);
    updateDisplay();
    updateTotals();

    // Load current user information first
    await loadCurrentUser();

    // Load products from database
    await loadProducts();

    console.log('POS - User loading completed, permissions ready');

    // Apply access control restrictions
    applyAccessControlRestrictions();

    // Initialize shift management
    await initializeShiftManagement();

    // Initialize maximize button state
    try {
        const isMaximized = await ipcRenderer.invoke('is-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error checking initial maximize state:', error);
    }

    console.log('POS - Initialization completed successfully');
});

// Time functions
function updateTime() {
    const now = new Date();
    document.getElementById('current-date').textContent = formatDate(now);
    document.getElementById('current-time').textContent = formatTime(now);
}

function formatDate(date) {
    return date.toLocaleDateString("en-US", { month: "2-digit", day: "2-digit", year: "2-digit" });
}

function formatTime(date) {
    return date.toLocaleTimeString("en-US", { hour12: false });
}

function formatCurrency(amount) {
    return amount.toFixed(2);
}

// Navigation functions with permission checks
function navigateToAdmin() {
    if (canAccessAdmin()) {
        console.log('POS - Navigating to admin (permission granted)');
        ipcRenderer.invoke('navigate-to', 'admin');
    } else {
        console.log('POS - Admin navigation blocked (no permission)');
        showAccessDeniedMessage('Admin Panel');
    }
}

function navigateToTheater() {
    if (canAccessTheater()) {
        console.log('POS - Navigating to theater (permission granted)');
        ipcRenderer.invoke('navigate-to', 'theater');
    } else {
        console.log('POS - Theater navigation blocked (no permission)');
        showAccessDeniedMessage('Theater Management');
    }
}

// Logout function
function logout() {
    if (confirm('Are you sure you want to logout?')) {
        // Clear any local data/state if needed
        try {
            ipcRenderer.invoke('logout').then(() => {
                console.log('Logout successful');
            }).catch((error) => {
                console.error('Logout error:', error);
            });
        } catch (error) {
            console.error('Logout error:', error);
        }
    }
}

// Window control functions
function closeApp() {
    ipcRenderer.invoke('close-app');
}

function minimizeApp() {
    ipcRenderer.invoke('minimize-app');
}

async function toggleFullscreen() {
    try {
        console.log('Toggling maximize...');
        const isMaximized = await ipcRenderer.invoke('toggle-fullscreen');
        console.log('Maximized state:', isMaximized);
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
            console.log('Updated button text to:', fullscreenBtn.textContent);
        } else {
            console.error('Maximize button not found');
        }
    } catch (error) {
        console.error('Error toggling maximize:', error);
    }
}

// Keypad functions
function handleNumberClick(num) {
    // Handle decimal point - only allow one decimal point
    if (num === '.') {
        if (currentInput.includes('.')) {
            return; // Don't add another decimal point
        }
        // If currentInput is empty or just "0", start with "0."
        if (currentInput === '' || currentInput === '0') {
            currentInput = '0.';
        } else {
            currentInput += '.';
        }
    } else {
        // Handle regular numbers
        currentInput += num;
    }
    updateDisplay();
}

function handleBackspace() {
    currentInput = currentInput.slice(0, -1);
    updateDisplay();
}

function handleClear() {
    currentInput = "";
    isEditingPrice = false;
    updateDisplay();
}

function handleEnter() {
    if (currentInput && cartItems.length > 0) {
        if (isEditingPrice) {
            // Update price of selected item
            const newPrice = parseFloat(currentInput);
            if (newPrice > 0 && selectedItemIndex < cartItems.length) {
                cartItems[selectedItemIndex].price = newPrice;
                cartItems[selectedItemIndex].value = cartItems[selectedItemIndex].quantity * newPrice;
                updateCartDisplay();
                updateTotals();
            }
            isEditingPrice = false;
        } else {
            // Update quantity of selected item
            const quantity = parseInt(currentInput);
            if (quantity > 0 && selectedItemIndex < cartItems.length) {
                cartItems[selectedItemIndex].quantity = quantity;
                cartItems[selectedItemIndex].value = quantity * cartItems[selectedItemIndex].price;
                updateCartDisplay();
                updateTotals();
            }
        }
        currentInput = "";
        updateDisplay();
    }
}

function handleEditPrice() {
    if (cartItems.length > 0 && selectedItemIndex < cartItems.length) {
        isEditingPrice = true;
        currentInput = cartItems[selectedItemIndex].price.toString();
        updateDisplay();
    }
}

function handleRemoveItem() {
    if (cartItems.length > 0 && selectedItemIndex < cartItems.length) {
        cartItems.splice(selectedItemIndex, 1);
        if (selectedItemIndex >= cartItems.length) {
            selectedItemIndex = Math.max(0, cartItems.length - 1);
        }
        updateCartDisplay();
        updateTotals();
    }
}

// Display functions
function updateDisplay() {
    const displayText = currentInput || "0";
    document.getElementById('current-input').textContent = displayText;
    
    const priceIndicator = document.getElementById('price-indicator');
    if (isEditingPrice) {
        priceIndicator.style.display = 'inline';
    } else {
        priceIndicator.style.display = 'none';
    }
}

// Cart functions
function addItemToCart(product) {
    const existingItemIndex = cartItems.findIndex(item => item.description === product.name);

    if (existingItemIndex >= 0) {
        // If item already exists, increase quantity
        cartItems[existingItemIndex].quantity += 1;
        cartItems[existingItemIndex].value = cartItems[existingItemIndex].quantity * cartItems[existingItemIndex].price;
    } else {
        // Add new item
        const newItem = {
            id: Date.now().toString(),
            qh: cartItems.length,
            description: product.name,
            quantity: 1,
            price: product.price,
            discount: 0,
            value: product.price,
            // Store additional product details for sales recording
            productId: product.id,
            barcode: product.barcode || '',
            category: product.category || '',
            subcategory: product.subcategory || '',
            isDeliItem: product.daily_item === 1 || product.daily_item === true
        };
        cartItems.push(newItem);
    }
    
    updateCartDisplay();
    updateTotals();
}

function removeItemFromCart(product) {
    const existingItemIndex = cartItems.findIndex(item => item.description === product.name);

    if (existingItemIndex >= 0) {
        if (cartItems[existingItemIndex].quantity > 1) {
            // Decrease quantity
            cartItems[existingItemIndex].quantity -= 1;
            cartItems[existingItemIndex].value = cartItems[existingItemIndex].quantity * cartItems[existingItemIndex].price;
        } else {
            // Remove item completely
            cartItems.splice(existingItemIndex, 1);
            if (selectedItemIndex >= cartItems.length) {
                selectedItemIndex = Math.max(0, cartItems.length - 1);
            }
        }
        updateCartDisplay();
        updateTotals();
    }
}

function updateCartDisplay() {
    const tbody = document.getElementById('cart-items');

    if (cartItems.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="empty-cart">
                    No items in cart. Click SELECT ITEM to add products.
                </td>
            </tr>
        `;
    } else {
        tbody.innerHTML = cartItems.map((item, index) => `
            <tr class="${selectedItemIndex === index ? 'selected' : ''}" onclick="selectItem(${index})">
                <td>${index}</td>
                <td>${item.description}</td>
                <td>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <button onclick="event.stopPropagation(); changeQuantity(${index}, -1)"
                                style="background: #aa0000; color: white; border: 1px solid #ff0000; width: 24px; height: 24px; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: bold;">-</button>
                        <span style="min-width: 30px; text-align: center; font-weight: bold;">${item.quantity}</span>
                        <button onclick="event.stopPropagation(); changeQuantity(${index}, 1)"
                                style="background: #00aa00; color: white; border: 1px solid #00ff00; width: 24px; height: 24px; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: bold;">+</button>
                    </div>
                </td>
                <td>${formatCurrency(item.price)}</td>
                <td>${formatCurrency(item.discount)}</td>
                <td>${formatCurrency(item.value)}</td>
            </tr>
        `).join('');
    }
}

function selectItem(index) {
    selectedItemIndex = index;
    updateCartDisplay();
}

// Function to change quantity directly from cart
function changeQuantity(index, change) {
    if (index >= 0 && index < cartItems.length) {
        const newQuantity = cartItems[index].quantity + change;

        if (newQuantity > 0) {
            cartItems[index].quantity = newQuantity;
            cartItems[index].value = newQuantity * cartItems[index].price;
            updateCartDisplay();
            updateTotals();
        } else if (newQuantity === 0) {
            // Remove item if quantity becomes 0
            cartItems.splice(index, 1);
            if (selectedItemIndex >= cartItems.length) {
                selectedItemIndex = Math.max(0, cartItems.length - 1);
            }
            updateCartDisplay();
            updateTotals();
        }
    }
}

// Calculation functions
function calculateSubtotal() {
    return cartItems.reduce((sum, item) => sum + item.value, 0);
}

function calculateTax() {
    return calculateSubtotal() * 0.06625;
}

function calculateTotal() {
    return calculateSubtotal() + calculateTax() - discountAmount;
}

function updateTotals() {
    document.getElementById('item-discount').textContent = formatCurrency(0.0);
    document.getElementById('discount-amount').textContent = formatCurrency(discountAmount);
    document.getElementById('subtotal').textContent = formatCurrency(calculateSubtotal());
    document.getElementById('tax-amount').textContent = formatCurrency(calculateTax());
    document.getElementById('item-count').textContent = cartItems.length;
    document.getElementById('cash-amount').textContent = formatCurrency(calculateTotal());
    document.getElementById('change-amount').textContent = formatCurrency(0.00);
    document.getElementById('total-amount').textContent = formatCurrency(calculateTotal());

    // Update modal footer if modal is open
    updateModalFooter();
}

// Checkout function
function handleCheckout() {
    if (cartItems.length > 0) {
        showCheckoutConfirmationModal();
    }
}

// Global variable to store selected payment method
let selectedPaymentMethod = 'Cash';

// Global variables for payment tracking
let currentPaymentAmount = '';
let paymentAmounts = {
    Cash: 0,
    Debit: 0,
    Credit: 0
};

function showCheckoutConfirmationModal() {
    // Create checkout confirmation modal
    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.id = 'checkout-confirmation-modal';

    // Reset payment amounts
    currentPaymentAmount = '';
    paymentAmounts = {
        Cash: calculateTotal(),
        Debit: 0,
        Credit: 0
    };

    // Generate product list HTML for display area
    const productListHTML = cartItems.map(item => `
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 12px; border-bottom: 1px solid #333; background-color: #1a1a1a; margin-bottom: 4px; border-radius: 4px;">
            <div style="flex: 1;">
                <div style="font-size: 16px; font-weight: bold; color: #00ff00;">${item.description}</div>
                <div style="font-size: 14px; color: #00cc00;">$${formatCurrency(item.price)} × ${item.quantity}</div>
            </div>
            <div style="text-align: right;">
                <div style="font-size: 18px; font-weight: bold; color: #00ff00;">$${formatCurrency(item.value)}</div>
            </div>
        </div>
    `).join('');

    modal.innerHTML = `
        <div class="modal-content" style="background-color: #000000; border: 2px solid #00ff00; color: #00ff00; width: 98vw; height: 95vh; max-width: 1400px; max-height: 95vh; overflow: hidden; display: flex; flex-direction: column; position: relative; margin: auto;">
            <div class="modal-header" style="flex-shrink: 0; padding: 12px 20px; border-bottom: 2px solid #666666; display: flex; justify-content: space-between; align-items: center; background-color: #000000;">
                <div class="modal-title" style="font-size: 40px; font-weight: 900; color: #00ff00;">CHECK OUT</div>
                <button class="close-btn" onclick="closeCheckoutModal()" style="background-color: #000000; color: #ff0000; border: 2px solid #ff0000; padding: 8px 16px; cursor: pointer; font-size: 24px; font-weight: bold; border-radius: 4px;">×</button>
            </div>

            <div style="flex: 1; display: flex; padding: 10px; gap: 10px; overflow: hidden;">
                <!-- Left Panel -->
                <div style="flex: 1; display: flex; flex-direction: column; gap: 10px; min-width: 0;">
                    <!-- Payment Input Field -->
                    <div style="background-color: #000000; border: 2px solid #00ff00; border-radius: 8px; padding: 10px;">
                        <div style="color: #00ff00; font-size: 18px; font-weight: bold; margin-bottom: 8px; text-align: center;">PAYMENT AMOUNT</div>
                        <input type="text" id="payment-input" readonly style="background-color: #1a1a1a; color: #00ff00; border: 2px solid #00ff00; padding: 12px; border-radius: 6px; font-size: 28px; font-weight: bold; width: 100%; text-align: center;" value="0.00">
                    </div>

                    <!-- Keypad -->
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; max-width: 350px;">
                        <button onclick="addToPaymentAmount('1')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">1</button>
                        <button onclick="addToPaymentAmount('2')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">2</button>
                        <button onclick="addToPaymentAmount('3')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">3</button>
                        <button onclick="addToPaymentAmount('4')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">4</button>
                        <button onclick="addToPaymentAmount('5')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">5</button>
                        <button onclick="addToPaymentAmount('6')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">6</button>
                        <button onclick="addToPaymentAmount('7')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">7</button>
                        <button onclick="addToPaymentAmount('8')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">8</button>
                        <button onclick="addToPaymentAmount('9')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">9</button>
                        <button onclick="addToPaymentAmount('.')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">.</button>
                        <button onclick="addToPaymentAmount('0')" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; font-size: 36px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">0</button>
                        <button onclick="clearPaymentAmount()" style="background-color: #aa0000; color: #ffffff; border: 2px solid #ff0000; font-size: 24px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; aspect-ratio: 1; transition: all 0.2s;">←</button>
                    </div>
                </div>

                <!-- Payment Method Section (Horizontal Layout) -->
                <div style="background-color: #000000; border: 2px solid #00ff00; border-radius: 8px; padding: 12px;">
                    <div style="color: #00ff00; font-size: 20px; font-weight: bold; margin-bottom: 10px; text-align: center;">PAYMENT METHODS</div>

                    <!-- Payment Method Buttons in Horizontal Layout -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 10px;">
                        <div style="display: flex; align-items: center; background-color: #1a1a1a; border: 2px solid #666; border-radius: 8px; padding: 10px; cursor: pointer; transition: all 0.2s;" onclick="selectPaymentMethod('Cash')" id="cash-payment-row">
                            <div style="width: 40px; height: 40px; background-color: #00ff00; border-radius: 6px; display: flex; align-items: center; justify-content: center; margin-right: 10px; color: #000000; font-weight: bold; font-size: 20px;">$</div>
                            <div style="flex: 1;">
                                <div style="color: #00ff00; font-weight: bold; font-size: 20px;">CASH →</div>
                            </div>
                            <div style="color: #00ff00; font-weight: 900; font-size: 22px;" id="cash-display">${formatCurrency(calculateTotal())}</div>
                            <button onclick="event.stopPropagation(); clearPaymentMethod('Cash')" style="background-color: #ff0000; color: #ffffff; border: none; padding: 6px 10px; margin-left: 8px; cursor: pointer; border-radius: 4px; font-size: 12px; font-weight: bold;">CLEAR</button>
                        </div>

                        <div style="display: flex; align-items: center; background-color: #1a1a1a; border: 2px solid #666; border-radius: 8px; padding: 10px; cursor: pointer; transition: all 0.2s;" onclick="selectPaymentMethod('Debit')" id="debit-payment-row">
                            <div style="width: 40px; height: 40px; background-color: #00ff00; border-radius: 6px; display: flex; align-items: center; justify-content: center; margin-right: 10px; color: #000000; font-weight: bold; font-size: 14px;">DB</div>
                            <div style="flex: 1;">
                                <div style="color: #00ff00; font-weight: bold; font-size: 20px;">DEBIT →</div>
                            </div>
                            <div style="color: #00ff00; font-weight: 900; font-size: 22px;" id="debit-display">0.00</div>
                            <button onclick="event.stopPropagation(); clearPaymentMethod('Debit')" style="background-color: #ff0000; color: #ffffff; border: none; padding: 6px 10px; margin-left: 8px; cursor: pointer; border-radius: 4px; font-size: 12px; font-weight: bold;">CLEAR</button>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 10px;">
                        <div style="display: flex; align-items: center; background-color: #1a1a1a; border: 2px solid #666; border-radius: 8px; padding: 10px; cursor: pointer; transition: all 0.2s;" onclick="selectPaymentMethod('Credit')" id="credit-payment-row">
                            <div style="width: 40px; height: 40px; background-color: #00ff00; border-radius: 6px; display: flex; align-items: center; justify-content: center; margin-right: 10px; color: #000000; font-weight: bold; font-size: 14px;">CR</div>
                            <div style="flex: 1;">
                                <div style="color: #00ff00; font-weight: bold; font-size: 20px;">CREDIT →</div>
                            </div>
                            <div style="color: #00ff00; font-weight: 900; font-size: 22px;" id="credit-display">0.00</div>
                            <button onclick="event.stopPropagation(); clearPaymentMethod('Credit')" style="background-color: #ff0000; color: #ffffff; border: none; padding: 6px 10px; margin-left: 8px; cursor: pointer; border-radius: 4px; font-size: 12px; font-weight: bold;">CLEAR</button>
                        </div>

                        <div style="display: flex; align-items: center; background-color: #1a1a1a; border: 2px solid #666; border-radius: 8px; padding: 10px;">
                            <div style="flex: 1;">
                                <div style="color: #ff0000; font-weight: bold; font-size: 20px;">CARD NO →</div>
                            </div>
                            <input type="text" placeholder="Enter card number" style="background-color: #000000; color: #00ff00; border: 2px solid #00ff00; padding: 8px; border-radius: 4px; font-size: 16px; width: 150px;" id="card-number-input">
                        </div>
                    </div>

                    <button onclick="clearAllPayments()" style="background-color: #aa0000; color: #ffffff; border: 2px solid #ff0000; font-size: 18px; font-weight: bold; padding: 12px; cursor: pointer; border-radius: 8px; width: 100%; transition: all 0.2s;">CLEAR CHANGE</button>

                    <!-- Product Display Area (Below Clear Change Button) -->
                    <div style="margin-top: 10px;">
                        <div style="color: #00ff00; font-size: 18px; font-weight: bold; margin-bottom: 8px; text-align: center;">ITEMS</div>
                        <div style="background-color: #1a1a1a; border: 2px solid #00ff00; border-radius: 6px; padding: 10px; height: 120px; overflow-y: auto;">
                            ${productListHTML}
                        </div>
                    </div>
                </div>

                <!-- Right Panel -->
                <div style="width: 320px; display: flex; flex-direction: column; gap: 10px;">
                    <!-- Item Count -->
                    <div style="background-color: #000000; border: 2px solid #00ff00; border-radius: 8px; padding: 15px; text-align: center;">
                        <div style="color: #00ff00; font-size: 22px; font-weight: bold; margin-bottom: 8px;">Item Count</div>
                        <div style="background-color: #1a1a1a; border: 2px solid #00ff00; border-radius: 6px; padding: 15px;">
                            <div style="color: #00ff00; font-size: 56px; font-weight: 900;">${cartItems.length}</div>
                        </div>
                    </div>

                    <!-- Net Total -->
                    <div style="background-color: #000000; border: 2px solid #00ff00; border-radius: 8px; padding: 15px; text-align: center;">
                        <div style="color: #00ff00; font-size: 22px; font-weight: bold; margin-bottom: 8px;">Net Total</div>
                        <div style="background-color: #1a1a1a; border: 2px solid #00ff00; border-radius: 6px; padding: 15px;">
                            <div style="color: #00ff00; font-size: 56px; font-weight: 900;">${formatCurrency(calculateTotal())}</div>
                        </div>
                    </div>

                    <!-- Change -->
                    <div style="background-color: #000000; border: 2px solid #00ff00; border-radius: 8px; padding: 15px; text-align: center;">
                        <div style="color: #00ff00; font-size: 22px; font-weight: bold; margin-bottom: 8px;">Change</div>
                        <div style="background-color: #1a1a1a; border: 2px solid #00ff00; border-radius: 6px; padding: 15px;">
                            <div style="color: #00ff00; font-size: 56px; font-weight: 900;" id="change-amount-display">0.00</div>
                        </div>
                    </div>

                    <!-- Close Bill Button -->
                    <button onclick="confirmCheckout()" style="background-color: #aa0000; color: #ffffff; border: 2px solid #ff0000; font-size: 26px; font-weight: 900; padding: 18px; cursor: pointer; border-radius: 8px; margin-top: 10px; transition: all 0.2s;">
                        CLOSE BILL
                    </button>
                </div>
            </div>

            <!-- Bottom Close Button -->
            <div style="flex-shrink: 0; padding: 16px; border-top: 2px solid #666666; text-align: center;">
                <button onclick="closeCheckoutModal()" style="background-color: #000000; color: #ff0000; border: 2px solid #ff0000; font-size: 20px; font-weight: 900; padding: 12px 40px; cursor: pointer; border-radius: 8px;">
                    ✗ CLOSE
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Initialize payment amounts and update displays
    updatePaymentDisplays();
}

// Function to add digits to payment amount
function addToPaymentAmount(digit) {
    if (digit === '.' && currentPaymentAmount.includes('.')) {
        return; // Don't allow multiple decimal points
    }

    currentPaymentAmount += digit;

    // Update the input field display
    const paymentInput = document.getElementById('payment-input');
    if (paymentInput) {
        paymentInput.value = currentPaymentAmount || '0.00';
    }

    // Update the selected payment method amount
    if (selectedPaymentMethod && selectedPaymentMethod !== 'Other') {
        const amount = parseFloat(currentPaymentAmount) || 0;
        paymentAmounts[selectedPaymentMethod] = amount;
        updatePaymentDisplays();
    }
}

// Function to clear payment amount input
function clearPaymentAmount() {
    currentPaymentAmount = currentPaymentAmount.slice(0, -1);

    // Update the input field display
    const paymentInput = document.getElementById('payment-input');
    if (paymentInput) {
        paymentInput.value = currentPaymentAmount || '0.00';
    }

    // Update the selected payment method amount
    if (selectedPaymentMethod && selectedPaymentMethod !== 'Other') {
        const amount = parseFloat(currentPaymentAmount) || 0;
        paymentAmounts[selectedPaymentMethod] = amount;
        updatePaymentDisplays();
    }
}

// Function to select payment method
function selectPaymentMethod(method) {
    selectedPaymentMethod = method;

    // Reset current input when switching methods
    currentPaymentAmount = '';

    // Update the input field display
    const paymentInput = document.getElementById('payment-input');
    if (paymentInput) {
        paymentInput.value = '0.00';
    }

    // Update visual selection
    updatePaymentMethodSelection();
}

// Function to clear specific payment method
function clearPaymentMethod(method) {
    paymentAmounts[method] = 0;
    if (selectedPaymentMethod === method) {
        currentPaymentAmount = '';
        // Update the input field display
        const paymentInput = document.getElementById('payment-input');
        if (paymentInput) {
            paymentInput.value = '0.00';
        }
    }
    updatePaymentDisplays();
}

// Function to clear all payments
function clearAllPayments() {
    paymentAmounts = {
        Cash: 0,
        Debit: 0,
        Credit: 0
    };
    currentPaymentAmount = '';

    // Update the input field display
    const paymentInput = document.getElementById('payment-input');
    if (paymentInput) {
        paymentInput.value = '0.00';
    }

    updatePaymentDisplays();
}

// Function to update payment displays
function updatePaymentDisplays() {
    const cashDisplay = document.getElementById('cash-display');
    const debitDisplay = document.getElementById('debit-display');
    const creditDisplay = document.getElementById('credit-display');
    const changeDisplay = document.getElementById('change-amount-display');

    if (cashDisplay) cashDisplay.textContent = formatCurrency(paymentAmounts.Cash);
    if (debitDisplay) debitDisplay.textContent = formatCurrency(paymentAmounts.Debit);
    if (creditDisplay) creditDisplay.textContent = formatCurrency(paymentAmounts.Credit);

    // Calculate change
    const totalPaid = paymentAmounts.Cash + paymentAmounts.Debit + paymentAmounts.Credit;
    const totalDue = calculateTotal();
    const change = Math.max(0, totalPaid - totalDue);

    if (changeDisplay) changeDisplay.textContent = formatCurrency(change);
}

// Function to update payment method selection visual
function updatePaymentMethodSelection() {
    const cashRow = document.getElementById('cash-payment-row');
    const debitRow = document.getElementById('debit-payment-row');
    const creditRow = document.getElementById('credit-payment-row');

    // Reset all styles to default (unselected state)
    if (cashRow) {
        cashRow.style.backgroundColor = '#1a1a1a';
        cashRow.style.borderColor = '#666';
        // Update text colors for cash (make them green like other methods)
        const cashText = cashRow.querySelector('div:nth-child(2) div');
        const cashAmount = cashRow.querySelector('div:nth-child(3)');
        if (cashText) cashText.style.color = '#00ff00';
        if (cashAmount) cashAmount.style.color = '#00ff00';
    }
    if (debitRow) {
        debitRow.style.backgroundColor = '#1a1a1a';
        debitRow.style.borderColor = '#666';
    }
    if (creditRow) {
        creditRow.style.backgroundColor = '#1a1a1a';
        creditRow.style.borderColor = '#666';
    }

    // Highlight selected method
    if (selectedPaymentMethod === 'Cash' && cashRow) {
        cashRow.style.backgroundColor = '#006600';
        cashRow.style.borderColor = '#00ff00';
        // Update text colors for selected cash (make them black)
        const cashText = cashRow.querySelector('div:nth-child(2) div');
        const cashAmount = cashRow.querySelector('div:nth-child(3)');
        if (cashText) cashText.style.color = '#000000';
        if (cashAmount) cashAmount.style.color = '#000000';
    } else if (selectedPaymentMethod === 'Debit' && debitRow) {
        debitRow.style.backgroundColor = '#006600';
        debitRow.style.borderColor = '#00ff00';
    } else if (selectedPaymentMethod === 'Credit' && creditRow) {
        creditRow.style.backgroundColor = '#006600';
        creditRow.style.borderColor = '#00ff00';
    }
}



// Confirm checkout and show success modal
async function confirmCheckout() {
    try {
        // Prepare sale data
        const saleData = {
            subtotal: calculateSubtotal(),
            taxAmount: calculateTax(),
            discountAmount: discountAmount,
            totalAmount: calculateTotal(),
            paymentCash: paymentAmounts.Cash || 0,
            paymentDebit: paymentAmounts.Debit || 0,
            paymentCredit: paymentAmounts.Credit || 0,
            paymentTotal: (paymentAmounts.Cash || 0) + (paymentAmounts.Debit || 0) + (paymentAmounts.Credit || 0),
            changeAmount: Math.max(0, ((paymentAmounts.Cash || 0) + (paymentAmounts.Debit || 0) + (paymentAmounts.Credit || 0)) - calculateTotal()),
            itemCount: cartItems.length,
            items: cartItems.map(item => ({
                productId: item.productId ? parseInt(item.productId) : null,
                name: item.description,
                barcode: item.barcode || '',
                category: item.category || '',
                subcategory: item.subcategory || '',
                quantity: item.quantity,
                price: item.price,
                total: item.value,
                isDeliItem: item.isDeliItem || false
            }))
        };

        console.log('POS - 💰 CHECKOUT: Saving sale data:', {
            total: saleData.totalAmount,
            items: saleData.itemCount,
            payments: {
                cash: saleData.paymentCash,
                debit: saleData.paymentDebit,
                credit: saleData.paymentCredit
            }
        });

        // Debug deli items
        const deliItems = saleData.items.filter(item => item.isDeliItem);
        console.log('🔍 DELI DEBUG - POS Checkout:');
        console.log(`  Total items: ${saleData.items.length}`);
        console.log(`  Deli items: ${deliItems.length}`);
        if (deliItems.length > 0) {
            console.log(`  Deli item details:`, deliItems);
        }

        // Save sale to database
        const result = await ipcRenderer.invoke('create-sale', saleData);

        if (result.success) {
            console.log(`POS - ✅ Sale saved successfully: ${result.data.saleId}`);
            closeCheckoutModal();
            showSuccessModal(result.data.saleId);
        } else {
            console.error('POS - Failed to save sale:', result.message);
            alert(`Failed to save sale: ${result.message}`);
        }
    } catch (error) {
        console.error('POS - Error during checkout:', error);
        alert('An error occurred during checkout. Please try again.');
    }
}

// Show final success modal
function showSuccessModal(saleId = null) {
    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.id = 'success-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">SUCCESSFUL SALE!</div>
                <button class="close-btn" onclick="closeCheckoutModal()">×</button>
            </div>
            <div style="text-align: center; padding: 32px; display: flex; flex-direction: column; gap: 32px;">
                <div style="background-color: #1a1a1a; border: 2px solid #00ff00; border-radius: 8px; padding: 32px;">
                    <div style="font-size: 96px; font-weight: 900; color: #00ff00; line-height: 1;">$${formatCurrency(calculateTotal())}</div>
                    ${saleId ? `<div style="font-size: 18px; color: #00ff00; margin-top: 16px; opacity: 0.8;">Sale ID: ${saleId}</div>` : ''}
                </div>

                <div style="background-color: #0a0a0a; border: 2px solid #00ff00; border-radius: 8px; padding: 20px; margin: 16px 0;">
                    <div style="font-size: 24px; font-weight: bold; color: #00ff00; margin-bottom: 12px;">Payment Method: ${selectedPaymentMethod}</div>
                    <div style="font-size: 18px; color: #00cc00;">Transaction completed successfully!</div>
                </div>

                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 24px;">
                    <div style="background-color: #1a1a1a; border: 1px solid #00ff00; border-radius: 8px; padding: 24px;">
                        <div style="font-size: 24px; font-weight: bold; color: #00cc00; margin-bottom: 8px;">Items Sold</div>
                        <div style="font-size: 48px; font-weight: 900; color: #00ff00;">${cartItems.length}</div>
                    </div>
                    <div style="background-color: #1a1a1a; border: 1px solid #00ff00; border-radius: 8px; padding: 24px;">
                        <div style="font-size: 24px; font-weight: bold; color: #00cc00; margin-bottom: 8px;">Subtotal</div>
                        <div style="font-size: 48px; font-weight: 900; color: #00ff00;">$${formatCurrency(calculateSubtotal())}</div>
                    </div>
                    <div style="background-color: #1a1a1a; border: 1px solid #00ff00; border-radius: 8px; padding: 24px;">
                        <div style="font-size: 24px; font-weight: bold; color: #00cc00; margin-bottom: 8px;">Tax (6.625%)</div>
                        <div style="font-size: 48px; font-weight: 900; color: #00ff00;">$${formatCurrency(calculateTax())}</div>
                    </div>
                </div>

                <div style="display: flex; gap: 24px; justify-content: center; margin-top: 48px;">
                    <button onclick="completeCheckout()" style="background-color: #00aa00; color: #000000; border: 2px solid #00ff00; font-size: 32px; font-weight: 900; padding: 24px 48px; cursor: pointer; border-radius: 8px;">
                        NEW TRANSACTION
                    </button>
                    <button onclick="closeCheckoutModal()" style="background-color: #aa0000; color: #ffffff; border: 2px solid #ff0000; font-size: 32px; font-weight: 900; padding: 24px 48px; cursor: pointer; border-radius: 8px;">
                        CLOSE
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function closeCheckoutModal() {
    const modal = document.querySelector('.modal');
    if (modal) {
        modal.remove();
    }
}

function completeCheckout() {
    cartItems = [];
    currentInput = "";
    selectedItemIndex = 0;
    discountAmount = 0;
    specialDiscount = false;
    isEditingPrice = false;
    selectedPaymentMethod = 'Cash'; // Reset payment method

    updateCartDisplay();
    updateTotals();
    updateDisplay();
    closeCheckoutModal();
}

// Draft Sales Functions
async function handleHold() {
    if (cartItems.length === 0) {
        alert('No items in cart to hold');
        return;
    }

    try {
        // Prepare draft sale data
        const draftData = {
            itemCount: cartItems.length,
            totalAmount: calculateTotal(),
            items: cartItems.map(item => ({
                productId: item.productId ? parseInt(item.productId) : null,
                name: item.description,
                barcode: item.barcode || '',
                category: item.category || '',
                subcategory: item.subcategory || '',
                quantity: item.quantity,
                price: item.price,
                total: item.value,
                discount: item.discount || 0
            }))
        };

        console.log('POS - 📋 HOLD: Saving draft sale:', {
            items: draftData.itemCount,
            total: draftData.totalAmount
        });

        // Save draft sale to database
        const result = await ipcRenderer.invoke('create-draft-sale', draftData);

        if (result.success) {
            console.log(`POS - ✅ Draft sale saved: ${result.data.draftSaleId}`);

            // Clear current cart
            cartItems = [];
            currentInput = "";
            selectedItemIndex = 0;
            discountAmount = 0;
            specialDiscount = false;
            isEditingPrice = false;

            updateCartDisplay();
            updateTotals();
            updateDisplay();

            alert(`Cart saved as draft: ${result.data.draftSaleId}`);
        } else {
            console.error('POS - Failed to save draft sale:', result.message);
            alert(`Failed to save draft: ${result.message}`);
        }
    } catch (error) {
        console.error('POS - Error saving draft sale:', error);
        alert('An error occurred while saving draft. Please try again.');
    }
}

async function handleRecall() {
    try {
        console.log('POS - 📋 RECALL: Loading draft sales...');

        // Get draft sales from database
        const result = await ipcRenderer.invoke('get-draft-sales');

        if (result.success) {
            if (result.drafts.length === 0) {
                alert('No draft sales found');
                return;
            }

            console.log(`POS - Found ${result.drafts.length} draft sales`);
            showDraftSalesModal(result.drafts);
        } else {
            console.error('POS - Failed to load draft sales:', result.message);
            alert(`Failed to load drafts: ${result.message}`);
        }
    } catch (error) {
        console.error('POS - Error loading draft sales:', error);
        alert('An error occurred while loading drafts. Please try again.');
    }
}

function showDraftSalesModal(drafts) {
    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.id = 'draft-sales-modal';

    modal.innerHTML = `
        <div class="modal-content" style="background-color: #000000; border: 2px solid #00ff00; color: #00ff00; width: 90vw; height: 85vh; max-width: 1200px; overflow: hidden; display: flex; flex-direction: column;">
            <div class="modal-header" style="flex-shrink: 0; padding: 16px 24px; border-bottom: 2px solid #666666; display: flex; justify-content: space-between; align-items: center;">
                <div class="modal-title" style="font-size: 32px; font-weight: 900; color: #00ff00;">RECALL DRAFT SALES</div>
                <button class="close-btn" onclick="closeDraftSalesModal()" style="background: none; border: none; color: #ff0000; font-size: 32px; cursor: pointer; padding: 0; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">×</button>
            </div>

            <div style="flex: 1; padding: 20px; overflow-y: auto;">
                <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 16px;">
                    ${drafts.map(draft => `
                        <div class="draft-card" onclick="selectDraftSale('${draft.draft_sale_id}')" style="
                            background-color: #1a1a1a;
                            border: 2px solid #00ff00;
                            border-radius: 8px;
                            padding: 16px;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            hover: background-color: #2a2a2a;
                        " onmouseover="this.style.backgroundColor='#2a2a2a'" onmouseout="this.style.backgroundColor='#1a1a1a'">
                            <div style="font-size: 18px; font-weight: bold; color: #00ff00; margin-bottom: 8px;">
                                ${draft.draft_sale_id}
                            </div>
                            <div style="font-size: 14px; color: #cccccc; margin-bottom: 4px;">
                                Items: ${draft.item_count}
                            </div>
                            <div style="font-size: 16px; font-weight: bold; color: #00ff00; margin-bottom: 8px;">
                                Total: $${parseFloat(draft.total_amount).toFixed(2)}
                            </div>
                            <div style="font-size: 12px; color: #888888;">
                                Created: ${new Date(draft.created_at).toLocaleString()}
                            </div>
                            <div style="font-size: 12px; color: #888888;">
                                By: ${draft.operator_name}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>

            <div style="flex-shrink: 0; padding: 16px; border-top: 2px solid #666666; text-align: center;">
                <button onclick="closeDraftSalesModal()" style="background-color: #aa0000; color: #ffffff; border: 2px solid #ff0000; font-size: 18px; font-weight: 900; padding: 12px 32px; cursor: pointer; border-radius: 8px;">
                    CLOSE
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function closeDraftSalesModal() {
    const modal = document.getElementById('draft-sales-modal');
    if (modal) {
        modal.remove();
    }
}

async function selectDraftSale(draftSaleId) {
    try {
        console.log(`POS - 📋 RECALL: Loading draft sale ${draftSaleId}...`);

        // Get draft sale details
        const result = await ipcRenderer.invoke('get-draft-sale-details', draftSaleId);

        if (result.success && result.draft) {
            const draft = result.draft;

            // Clear current cart
            cartItems = [];
            currentInput = "";
            selectedItemIndex = 0;
            discountAmount = 0;
            specialDiscount = false;
            isEditingPrice = false;

            // Load draft items into cart
            draft.items.forEach(item => {
                const cartItem = {
                    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
                    qh: cartItems.length,
                    description: item.product_name,
                    quantity: item.quantity,
                    price: parseFloat(item.unit_price),
                    discount: parseFloat(item.discount) || 0,
                    value: parseFloat(item.total_price),
                    // Store additional product details
                    productId: item.product_id,
                    barcode: item.product_barcode || '',
                    category: item.product_category || '',
                    subcategory: item.product_subcategory || ''
                };
                cartItems.push(cartItem);
            });

            // Update displays
            updateCartDisplay();
            updateTotals();
            updateDisplay();

            // Close modal
            closeDraftSalesModal();

            console.log(`POS - ✅ Draft sale ${draftSaleId} recalled with ${draft.items.length} items`);
            alert(`Draft sale recalled: ${draftSaleId}\nItems loaded: ${draft.items.length}`);

            // Optionally delete the draft sale after successful recall
            if (confirm('Delete this draft sale now that it has been recalled?')) {
                const deleteResult = await ipcRenderer.invoke('delete-draft-sale', draftSaleId);
                if (deleteResult.success) {
                    console.log(`POS - ✅ Draft sale ${draftSaleId} deleted after recall`);
                }
            }
        } else {
            console.error('POS - Failed to load draft sale details:', result.message);
            alert(`Failed to load draft sale: ${result.message}`);
        }
    } catch (error) {
        console.error('POS - Error recalling draft sale:', error);
        alert('An error occurred while recalling draft sale. Please try again.');
    }
}

// Draft Sales Functions
async function handleHold() {
    if (cartItems.length === 0) {
        alert('No items in cart to hold');
        return;
    }

    try {
        // Prepare draft sale data
        const draftData = {
            itemCount: cartItems.length,
            totalAmount: calculateTotal(),
            items: cartItems.map(item => ({
                productId: item.productId ? parseInt(item.productId) : null,
                name: item.description,
                barcode: item.barcode || '',
                category: item.category || '',
                subcategory: item.subcategory || '',
                quantity: item.quantity,
                price: item.price,
                total: item.value,
                discount: item.discount || 0
            }))
        };

        console.log('POS - 📋 HOLD: Saving draft sale:', {
            items: draftData.itemCount,
            total: draftData.totalAmount
        });

        // Save draft sale to database
        const result = await ipcRenderer.invoke('create-draft-sale', draftData);

        if (result.success) {
            console.log(`POS - ✅ Draft sale saved: ${result.data.draftSaleId}`);

            // Clear current cart
            cartItems = [];
            currentInput = "";
            selectedItemIndex = 0;
            discountAmount = 0;
            specialDiscount = false;
            isEditingPrice = false;

            updateCartDisplay();
            updateTotals();
            updateDisplay();

            alert(`Cart saved as draft: ${result.data.draftSaleId}`);
        } else {
            console.error('POS - Failed to save draft sale:', result.message);
            alert(`Failed to save draft: ${result.message}`);
        }
    } catch (error) {
        console.error('POS - Error saving draft sale:', error);
        alert('An error occurred while saving draft. Please try again.');
    }
}

async function handleRecall() {
    try {
        console.log('POS - 📋 RECALL: Loading draft sales...');

        // Get draft sales from database
        const result = await ipcRenderer.invoke('get-draft-sales');

        if (result.success) {
            if (result.drafts.length === 0) {
                alert('No draft sales found');
                return;
            }

            console.log(`POS - Found ${result.drafts.length} draft sales`);
            showDraftSalesModal(result.drafts);
        } else {
            console.error('POS - Failed to load draft sales:', result.message);
            alert(`Failed to load drafts: ${result.message}`);
        }
    } catch (error) {
        console.error('POS - Error loading draft sales:', error);
        alert('An error occurred while loading drafts. Please try again.');
    }
}

// Product modal functions
let selectedCategory = null;
let selectedSubcategory = null;
let sortOrder = 'asc'; // 'asc' or 'desc'
let priceFilter = 'all'; // 'all', 'low-to-high', 'high-to-low'

async function showProductModal() {
    console.log('POS - showProductModal called');
    selectedCategory = null;
    selectedSubcategory = null;
    sortOrder = 'asc';
    priceFilter = 'all';

    // Always refresh products to ensure we have the latest data
    console.log('POS - Loading products from database...');
    await loadProducts();

    console.log(`POS - Opening product modal with ${demoProducts.length} products available`);

    if (demoProducts.length === 0) {
        console.error('POS - No products loaded from database!');
        alert('No products found in database. Please add products first.');
        return;
    }

    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.id = 'product-modal';
    modal.innerHTML = `
        <div style="background-color: #ffffff; border: none; border-radius: 16px; color: #333333; width: 95vw; height: 90vh; max-width: 1400px; overflow: hidden; display: flex; flex-direction: column; position: relative; margin: auto; box-shadow: 0 20px 60px rgba(0,0,0,0.3);">
            <!-- Header -->
            <div style="flex-shrink: 0; padding: 24px 32px; border-bottom: 1px solid #e5e7eb; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <h2 style="font-size: 32px; font-weight: 700; margin: 0; color: white;">Select Products</h2>
                    <div style="display: flex; gap: 12px; align-items: center;">
                        <button onclick="refreshProductsInModal()" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 8px 16px; border-radius: 25px; cursor: pointer; font-size: 14px; font-weight: 500; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">🔄 Refresh</button>
                        <button onclick="closeProductModal()" style="background: rgba(255,255,255,0.2); color: white; border: none; width: 48px; height: 48px; border-radius: 50%; cursor: pointer; font-size: 24px; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">×</button>
                    </div>
                </div>
            </div>

            <!-- Category Filter Bar -->
            <div style="flex-shrink: 0; padding: 20px 32px; background-color: #f8fafc; border-bottom: 1px solid #e5e7eb;">
                <div style="display: flex; gap: 12px; align-items: center; flex-wrap: wrap;">
                    <span style="font-weight: 600; color: #374151; margin-right: 8px;">Categories:</span>
                    <button onclick="selectCategory(null)" id="category-all" style="padding: 8px 16px; border: 2px solid #667eea; background: #667eea; color: #000000; border-radius: 25px; cursor: pointer; font-weight: 700; transition: all 0.3s ease;">All Products</button>
                    ${[...new Set(demoProducts.map(p => p.category))].map(category => `
                        <button onclick="selectCategory('${category}')" id="category-${category.replace(/[^a-zA-Z0-9]/g, '')}" style="padding: 8px 16px; border: 2px solid #e5e7eb; background: #f8f9fa; color: #000000; border-radius: 25px; cursor: pointer; font-weight: 600; transition: all 0.3s ease;">${category}</button>
                    `).join('')}
                </div>
            </div>

            <!-- Subcategory Filter Bar -->
            <div id="subcategory-filter-bar" style="flex-shrink: 0; padding: 15px 32px; background-color: #f1f5f9; border-bottom: 1px solid #e5e7eb; display: none;">
                <div style="display: flex; gap: 12px; align-items: center; flex-wrap: wrap;">
                    <span style="font-weight: 600; color: #374151; margin-right: 8px;">Subcategories:</span>
                    <button onclick="selectSubcategory(null)" id="subcategory-all" style="padding: 6px 14px; border: 2px solid #10b981; background: #10b981; color: #000000; border-radius: 20px; cursor: pointer; font-weight: 700; transition: all 0.3s ease; font-size: 14px;">All Items</button>
                    <div id="subcategories-list" style="display: flex; gap: 8px; flex-wrap: wrap;">
                        <!-- Subcategories will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Sorting and Price Filter Bar -->
            <div style="flex-shrink: 0; padding: 15px 32px; background-color: #e2e8f0; border-bottom: 1px solid #e5e7eb;">
                <div style="display: flex; gap: 20px; align-items: center; flex-wrap: wrap;">
                    <!-- Sorting Controls -->
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <span style="font-weight: 600; color: #374151; margin-right: 8px;">Sort by Barcode:</span>
                        <button onclick="setSortOrder('asc')" id="sort-asc" style="padding: 6px 12px; border: 2px solid #3b82f6; background: #3b82f6; color: #000000; border-radius: 18px; cursor: pointer; font-weight: 700; transition: all 0.3s ease; font-size: 13px;">Ascending ↑</button>
                        <button onclick="setSortOrder('desc')" id="sort-desc" style="padding: 6px 12px; border: 2px solid #e5e7eb; background: #f8f9fa; color: #000000; border-radius: 18px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; font-size: 13px;">Descending ↓</button>
                    </div>

                    <!-- Price Filter Controls -->
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <span style="font-weight: 600; color: #374151; margin-right: 8px;">Price:</span>
                        <button onclick="setPriceFilter('all')" id="price-all" style="padding: 6px 12px; border: 2px solid #10b981; background: #10b981; color: #000000; border-radius: 18px; cursor: pointer; font-weight: 700; transition: all 0.3s ease; font-size: 13px;">All</button>
                        <button onclick="setPriceFilter('low-to-high')" id="price-low-high" style="padding: 6px 12px; border: 2px solid #e5e7eb; background: #f8f9fa; color: #000000; border-radius: 18px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; font-size: 13px;">$ Low → High</button>
                        <button onclick="setPriceFilter('high-to-low')" id="price-high-low" style="padding: 6px 12px; border: 2px solid #e5e7eb; background: #f8f9fa; color: #000000; border-radius: 18px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; font-size: 13px;">$ High → Low</button>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div style="flex: 1; padding: 24px 32px; overflow-y: auto; background-color: #ffffff;">
                <div id="products-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;">
                    <!-- Products will be populated here -->
                </div>
                <div id="no-products" style="display: none; text-align: center; padding: 60px 20px; color: #9ca3af;">
                    <div style="font-size: 48px; margin-bottom: 16px;">📦</div>
                    <div style="font-size: 18px; font-weight: 500;">No products found</div>
                    <div style="font-size: 14px; margin-top: 8px;">Try selecting a different category</div>
                </div>
            </div>

            <!-- Footer -->
            <div id="modal-footer" style="flex-shrink: 0; padding: 20px 32px; background-color: #f8fafc; border-top: 1px solid #e5e7eb;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="color: #374151; font-weight: 600; font-size: 16px;">
                        Items in Cart: <span style="color: #667eea; font-size: 20px; font-weight: 700;">${cartItems.length}</span>
                    </div>
                    <div style="color: #374151; font-weight: 600; font-size: 16px;">
                        Total: <span style="color: #10b981; font-size: 24px; font-weight: 700;">$${formatCurrency(calculateTotal())}</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Populate the products grid with all available products
    populateProductsGrid();

    // Log the current state for debugging
    console.log(`POS - Product modal opened successfully with ${demoProducts.length} products displayed`);
}

function closeProductModal() {
    const modal = document.getElementById('product-modal');
    if (modal) {
        modal.remove();
    }
}

// Function to refresh products in the modal
async function refreshProductsInModal() {
    console.log('POS - Refreshing products from database...');
    await loadProducts();
    populateProductsGrid();
    console.log(`POS - Products refreshed, now showing ${demoProducts.length} products`);
}

// Sorting and filtering functions
function setSortOrder(order) {
    sortOrder = order;
    priceFilter = 'all'; // Reset price filter when using barcode sort
    console.log(`POS - Sort order set to: ${order} (by barcode), price filter reset to 'all'`);

    // Update barcode sort button styles with black text always visible
    const ascBtn = document.getElementById('sort-asc');
    const descBtn = document.getElementById('sort-desc');

    if (order === 'asc') {
        if (ascBtn) {
            ascBtn.style.background = '#3b82f6';
            ascBtn.style.color = '#000000';
            ascBtn.style.borderColor = '#3b82f6';
            ascBtn.style.fontWeight = '700';
        }

        if (descBtn) {
            descBtn.style.background = '#f8f9fa';
            descBtn.style.color = '#000000';
            descBtn.style.borderColor = '#e5e7eb';
            descBtn.style.fontWeight = '600';
        }
    } else {
        if (descBtn) {
            descBtn.style.background = '#3b82f6';
            descBtn.style.color = '#000000';
            descBtn.style.borderColor = '#3b82f6';
            descBtn.style.fontWeight = '700';
        }

        if (ascBtn) {
            ascBtn.style.background = '#f8f9fa';
            ascBtn.style.color = '#000000';
            ascBtn.style.borderColor = '#e5e7eb';
            ascBtn.style.fontWeight = '600';
        }
    }

    // Reset price filter buttons to default
    resetPriceFilterButtons();

    populateProductsGrid();
}

function setPriceFilter(filter) {
    priceFilter = filter;

    // Reset barcode sort when using price filter (except for 'all')
    if (filter !== 'all') {
        sortOrder = 'asc'; // Reset to default
        resetBarcodeFilterButtons();
    }

    console.log(`POS - Price filter set to: ${filter}, barcode sort reset if needed`);

    // Update button styles with black text always visible
    const allBtn = document.getElementById('price-all');
    const lowHighBtn = document.getElementById('price-low-high');
    const highLowBtn = document.getElementById('price-high-low');

    // Reset all buttons to default state with black text
    [allBtn, lowHighBtn, highLowBtn].forEach(btn => {
        if (btn) {
            btn.style.background = '#f8f9fa';
            btn.style.color = '#000000';
            btn.style.borderColor = '#e5e7eb';
            btn.style.fontWeight = '600';
        }
    });

    // Highlight selected button with black text
    let selectedBtn;
    switch (filter) {
        case 'all':
            selectedBtn = allBtn;
            break;
        case 'low-to-high':
            selectedBtn = lowHighBtn;
            break;
        case 'high-to-low':
            selectedBtn = highLowBtn;
            break;
    }

    if (selectedBtn) {
        selectedBtn.style.background = '#10b981';
        selectedBtn.style.color = '#000000';
        selectedBtn.style.borderColor = '#10b981';
        selectedBtn.style.fontWeight = '700';
    }

    populateProductsGrid();
}

// Helper functions to reset button styles
function resetPriceFilterButtons() {
    const allBtn = document.getElementById('price-all');
    const lowHighBtn = document.getElementById('price-low-high');
    const highLowBtn = document.getElementById('price-high-low');

    [allBtn, lowHighBtn, highLowBtn].forEach(btn => {
        if (btn) {
            btn.style.background = '#f8f9fa';
            btn.style.color = '#000000';
            btn.style.borderColor = '#e5e7eb';
            btn.style.fontWeight = '600';
        }
    });

    // Highlight 'All' button as default
    if (allBtn) {
        allBtn.style.background = '#10b981';
        allBtn.style.color = '#000000';
        allBtn.style.borderColor = '#10b981';
        allBtn.style.fontWeight = '700';
    }
}

function resetBarcodeFilterButtons() {
    const ascBtn = document.getElementById('sort-asc');
    const descBtn = document.getElementById('sort-desc');

    if (ascBtn) {
        ascBtn.style.background = '#3b82f6';
        ascBtn.style.color = '#000000';
        ascBtn.style.borderColor = '#3b82f6';
        ascBtn.style.fontWeight = '700';
    }

    if (descBtn) {
        descBtn.style.background = '#f8f9fa';
        descBtn.style.color = '#000000';
        descBtn.style.borderColor = '#e5e7eb';
        descBtn.style.fontWeight = '600';
    }
}

function populateCategories() {
    const categories = [...new Set(demoProducts.map(p => p.category))];
    const categoriesList = document.getElementById('categories-list');

    categoriesList.innerHTML = categories.map(category => `
        <div onclick="selectCategory('${category}')"
             style="padding: 12px; margin-bottom: 8px; cursor: pointer; border: 2px solid #666666; border-radius: 8px; font-size: 16px; font-weight: bold; transition: all 0.3s ease; background-color: #000000; color: #00ff00;"
             onmouseover="this.style.borderColor='#00ff00'; this.style.backgroundColor='#333333';"
             onmouseout="this.style.borderColor='#666666'; this.style.backgroundColor='#000000';"
             id="category-${category.replace(/[^a-zA-Z0-9]/g, '')}">
            ${category}
        </div>
    `).join('');
}

function selectCategory(category) {
    selectedCategory = category;
    selectedSubcategory = null;

    // Update category selection visual with black text always
    document.querySelectorAll('[id^="category-"]').forEach(el => {
        el.style.backgroundColor = '#f8f9fa';
        el.style.color = '#000000';
        el.style.borderColor = '#e5e7eb';
        el.style.fontWeight = '600';
    });

    if (category) {
        const selectedEl = document.getElementById(`category-${category.replace(/[^a-zA-Z0-9]/g, '')}`);
        if (selectedEl) {
            selectedEl.style.backgroundColor = '#667eea';
            selectedEl.style.color = '#000000';
            selectedEl.style.borderColor = '#667eea';
            selectedEl.style.fontWeight = '700';
        }
    } else {
        const allBtn = document.getElementById('category-all');
        if (allBtn) {
            allBtn.style.backgroundColor = '#667eea';
            allBtn.style.color = '#000000';
            allBtn.style.borderColor = '#667eea';
            allBtn.style.fontWeight = '700';
        }
    }

    // Show/hide subcategory filter bar and populate subcategories
    const subcategoryBar = document.getElementById('subcategory-filter-bar');
    if (category) {
        subcategoryBar.style.display = 'block';
        populateSubcategories();
    } else {
        subcategoryBar.style.display = 'none';
    }

    populateProductsGrid();
}

function populateSubcategories() {
    const subcategoriesList = document.getElementById('subcategories-list');

    if (!selectedCategory) {
        subcategoriesList.innerHTML = '';
        return;
    }

    const subcategories = [...new Set(
        demoProducts
            .filter(p => p.category === selectedCategory && p.subcategory)
            .map(p => p.subcategory)
    )];

    if (subcategories.length === 0) {
        subcategoriesList.innerHTML = '<div style="color: #9ca3af; font-size: 14px; padding: 8px 14px; font-style: italic;">No subcategories available</div>';
        return;
    }

    subcategoriesList.innerHTML = subcategories.map(subcategory => `
        <button onclick="selectSubcategory('${subcategory}')"
                id="subcategory-${subcategory.replace(/[^a-zA-Z0-9]/g, '')}"
                style="padding: 6px 14px; border: 2px solid #e5e7eb; background: #f8f9fa; color: #000000; border-radius: 20px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; font-size: 14px;">
            ${subcategory}
        </button>
    `).join('');
}

function selectSubcategory(subcategory) {
    selectedSubcategory = subcategory;

    // Update subcategory selection visual with black text always
    document.querySelectorAll('[id^="subcategory-"]').forEach(el => {
        el.style.backgroundColor = '#f8f9fa';
        el.style.color = '#000000';
        el.style.borderColor = '#e5e7eb';
        el.style.fontWeight = '600';
    });

    // Update "All Items" button
    const allBtn = document.getElementById('subcategory-all');
    if (allBtn) {
        allBtn.style.backgroundColor = '#f8f9fa';
        allBtn.style.color = '#000000';
        allBtn.style.borderColor = '#e5e7eb';
        allBtn.style.fontWeight = '600';
    }

    if (subcategory) {
        const selectedEl = document.getElementById(`subcategory-${subcategory.replace(/[^a-zA-Z0-9]/g, '')}`);
        if (selectedEl) {
            selectedEl.style.backgroundColor = '#10b981';
            selectedEl.style.color = '#000000';
            selectedEl.style.borderColor = '#10b981';
            selectedEl.style.fontWeight = '700';
        }
    } else {
        // If subcategory is null, highlight "All Items" button
        if (allBtn) {
            allBtn.style.backgroundColor = '#10b981';
            allBtn.style.color = '#000000';
            allBtn.style.borderColor = '#10b981';
            allBtn.style.fontWeight = '700';
        }
    }

    populateProductsGrid();
}

function getFilteredProducts() {
    console.log(`POS - getFilteredProducts called. Category: ${selectedCategory}, subcategory: ${selectedSubcategory}, sort: ${sortOrder}, price: ${priceFilter}`);
    console.log(`POS - Total products to filter: ${demoProducts.length}`);

    // Start with all products or filter by category
    let filtered;
    if (!selectedCategory) {
        console.log('POS - No category selected, using all products');
        filtered = [...demoProducts];
    } else {
        filtered = demoProducts.filter(p => p.category === selectedCategory);
        console.log(`POS - After category filter: ${filtered.length} products`);
    }

    // Apply subcategory filter
    if (selectedSubcategory) {
        filtered = filtered.filter(p => p.subcategory === selectedSubcategory);
        console.log(`POS - After subcategory filter: ${filtered.length} products`);
    }

    // Apply sorting - either by barcode or by price, not both
    if (priceFilter === 'low-to-high') {
        filtered.sort((a, b) => a.price - b.price);
        console.log('POS - Applied price sort (Low to High)');
    } else if (priceFilter === 'high-to-low') {
        filtered.sort((a, b) => b.price - a.price);
        console.log('POS - Applied price sort (High to Low)');
    } else {
        // Apply barcode sorting when price filter is 'all'
        if (sortOrder === 'asc') {
            filtered.sort((a, b) => {
                const barcodeA = a.barcode || '';
                const barcodeB = b.barcode || '';
                return barcodeA.localeCompare(barcodeB, undefined, { numeric: true });
            });
            console.log('POS - Applied ascending sort by barcode');
        } else if (sortOrder === 'desc') {
            filtered.sort((a, b) => {
                const barcodeA = a.barcode || '';
                const barcodeB = b.barcode || '';
                return barcodeB.localeCompare(barcodeA, undefined, { numeric: true });
            });
            console.log('POS - Applied descending sort by barcode');
        }
    }

    console.log(`POS - Final filtered products: ${filtered.length}`);
    return filtered;
}

function getItemQuantityInCart(productName) {
    const item = cartItems.find(item => item.description === productName);
    return item ? item.quantity : 0;
}

function populateItems() {
    const itemsList = document.getElementById('items-list');
    const filteredProducts = getFilteredProducts();

    if (filteredProducts.length === 0) {
        itemsList.innerHTML = `
            <div style="color: #666666; text-align: center; padding: 48px; font-size: 18px;">
                ${selectedCategory ? "No items in this category" : "Select a category to view items"}
            </div>
        `;
        return;
    }

    itemsList.innerHTML = filteredProducts.map(product => {
        const quantityInCart = getItemQuantityInCart(product.name);
        return `
            <div style="display: flex; align-items: center; justify-content: space-between; padding: 16px; border-bottom: 2px solid #333333; transition: background-color 0.3s ease; ${quantityInCart > 0 ? 'background-color: #333333;' : ''}"
                 onmouseover="this.style.backgroundColor='#333333';"
                 onmouseout="this.style.backgroundColor='${quantityInCart > 0 ? '#333333' : 'transparent'}';">
                <!-- Product Info -->
                <div style="flex: 1;">
                    <div style="color: #00ff00; font-weight: bold; font-size: 18px; margin-bottom: 8px;">
                        ${product.name}
                    </div>
                    <div style="color: #ff0000; font-weight: 900; font-size: 20px; margin-bottom: 4px;">
                        $${formatCurrency(product.price)}
                    </div>
                    ${product.subcategory ? `<div style="color: #666666; font-size: 14px;">${product.subcategory}</div>` : ''}
                </div>

                <!-- Quantity and Controls -->
                <div style="display: flex; align-items: center; gap: 12px;">
                    ${quantityInCart > 0 ? `
                        <div style="background-color: #00ff00; color: #000000; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: 900;">
                            ${quantityInCart}
                        </div>
                    ` : ''}
                    <button onclick="removeItemFromModal('${product.name}')"
                            ${quantityInCart === 0 ? 'disabled' : ''}
                            style="width: 48px; height: 48px; padding: 0; font-size: 14px; font-weight: 900; cursor: ${quantityInCart > 0 ? 'pointer' : 'not-allowed'}; border-radius: 4px; border: 2px solid; background-color: ${quantityInCart > 0 ? '#660000' : '#333333'}; color: ${quantityInCart > 0 ? '#ff0000' : '#666666'}; border-color: ${quantityInCart > 0 ? '#ff0000' : '#666666'}; transition: all 0.3s ease;"
                            ${quantityInCart > 0 ? 'onmouseover="this.style.backgroundColor=\'#990000\';" onmouseout="this.style.backgroundColor=\'#660000\';"' : ''}>
                        −
                    </button>
                    <button onclick="addItemFromModal('${product.name}')"
                            style="width: 48px; height: 48px; padding: 0; font-size: 14px; font-weight: 900; cursor: pointer; border-radius: 4px; border: 2px solid #00ff00; background-color: #006600; color: #00ff00; transition: all 0.3s ease;"
                            onmouseover="this.style.backgroundColor='#009900';" onmouseout="this.style.backgroundColor='#006600';">
                        ✓
                    </button>
                </div>
            </div>
        `;
    }).join('');
}

function addItemFromModal(productName) {
    const product = demoProducts.find(p => p.name === productName);
    if (product) {
        addItemToCart(product);
        populateProductsGrid(); // Refresh the products grid to update quantities
        updateModalFooter();
    }
}

function removeItemFromModal(productName) {
    const product = demoProducts.find(p => p.name === productName);
    if (product) {
        removeItemFromCart(product);
        populateProductsGrid(); // Refresh the products grid to update quantities
        updateModalFooter();
    }
}

function updateModalFooter() {
    const footer = document.getElementById('modal-footer');
    if (footer) {
        footer.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="color: #374151; font-weight: 600; font-size: 16px;">
                    Items in Cart: <span style="color: #667eea; font-size: 20px; font-weight: 700;">${cartItems.length}</span>
                </div>
                <div style="color: #374151; font-weight: 600; font-size: 16px;">
                    Total: <span style="color: #10b981; font-size: 24px; font-weight: 700;">$${formatCurrency(calculateTotal())}</span>
                </div>
            </div>
        `;
    }
}

function populateProductsGrid() {
    const productsGrid = document.getElementById('products-grid');
    const noProducts = document.getElementById('no-products');

    console.log(`POS - populateProductsGrid called. Grid element exists: ${!!productsGrid}`);
    console.log(`POS - Total products available: ${demoProducts.length}`);

    if (!productsGrid) {
        console.log('POS - Products grid element not found, modal may not be open');
        return;
    }

    const filteredProducts = getFilteredProducts();

    console.log(`POS - Populating product grid with ${filteredProducts.length} filtered products (total: ${demoProducts.length})`);
    console.log('POS - Sample filtered product:', filteredProducts[0]);

    if (filteredProducts.length === 0) {
        productsGrid.style.display = 'none';
        if (noProducts) {
            noProducts.style.display = 'block';
        }
        console.log('POS - No products to display in grid');
        return;
    }

    productsGrid.style.display = 'grid';
    noProducts.style.display = 'none';

    productsGrid.innerHTML = filteredProducts.map(product => {
        const quantityInCart = getItemQuantityInCart(product.name);
        return `
            <div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; transition: all 0.3s ease; box-shadow: 0 2px 8px rgba(0,0,0,0.1); ${quantityInCart > 0 ? 'border-color: #667eea; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);' : ''}" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(0,0,0,0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='${quantityInCart > 0 ? '0 4px 12px rgba(102, 126, 234, 0.15)' : '0 2px 8px rgba(0,0,0,0.1)'}'">
                <!-- Product Image -->
                <div style="position: relative; height: 200px; background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); display: flex; align-items: center; justify-content: center; overflow: hidden;">
                    <img src="${product.image}" alt="${product.name}" style="width: 100%; height: 100%; object-fit: cover;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div style="display: none; width: 100%; height: 100%; align-items: center; justify-content: center; background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); color: #9ca3af; font-size: 48px;">📦</div>
                    ${quantityInCart > 0 ? `
                        <div style="position: absolute; top: 12px; right: 12px; background: #667eea; color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: 700; box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);">
                            ${quantityInCart}
                        </div>
                    ` : ''}
                </div>

                <!-- Product Info -->
                <div style="padding: 16px;">
                    <h3 style="font-size: 16px; font-weight: 600; color: #1f2937; margin: 0 0 8px 0; line-height: 1.4; height: 44px; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">${product.name}</h3>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <span style="font-size: 20px; font-weight: 700; color: #10b981;">$${formatCurrency(product.price)}</span>
                        <span style="font-size: 12px; color: #6b7280; background: #f3f4f6; padding: 4px 8px; border-radius: 12px;">${product.subcategory || product.category}</span>
                    </div>

                    <!-- Action Buttons -->
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <button onclick="removeItemFromModal('${product.name}')" ${quantityInCart === 0 ? 'disabled' : ''} style="flex: 1; padding: 8px; border: 2px solid ${quantityInCart > 0 ? '#ef4444' : '#e5e7eb'}; background: ${quantityInCart > 0 ? '#fef2f2' : '#f9fafb'}; color: ${quantityInCart > 0 ? '#ef4444' : '#9ca3af'}; border-radius: 8px; cursor: ${quantityInCart > 0 ? 'pointer' : 'not-allowed'}; font-weight: 600; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center;" ${quantityInCart > 0 ? `onmouseover="this.style.background='#fee2e2'" onmouseout="this.style.background='#fef2f2'"` : ''}>
                            <span style="font-size: 18px;">−</span>
                        </button>
                        <button onclick="addItemFromModal('${product.name}')" style="flex: 2; padding: 10px; border: 2px solid #10b981; background: #10b981; color: white; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; gap: 6px;" onmouseover="this.style.background='#059669'" onmouseout="this.style.background='#10b981'">
                            <span style="font-size: 16px;">+</span>
                            <span>Add to Cart</span>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// Quick Action Modal Functions
function showQuickActionModal() {
    const modal = document.createElement('div');
    modal.className = 'quick-action-modal show';
    modal.id = 'quick-action-modal';

    modal.innerHTML = `
        <div class="quick-action-modal-content">
            <div class="quick-action-modal-header">
                <h2 class="quick-action-modal-title">Quick Actions</h2>
            </div>
            <div class="quick-action-modal-body">
                <div class="quick-action-grid">
                    <div class="quick-action-dropdown">
                        <button class="quick-action-btn dropdown-btn" onclick="toggleDailySaleReportDropdown()">
                            📊 Daily Sale Report ▼
                        </button>
                        <div id="daily-sale-dropdown" class="quick-action-dropdown-content" style="display: none;">
                            <a href="#" onclick="handlePrintDailySaleReport(); hideDailySaleReportDropdown();">🖨️ Print Report</a>
                            <a href="#" onclick="handleEmailDailySaleReport(); hideDailySaleReportDropdown();">📧 Email Report</a>
                        </div>
                    </div>

                    <div class="quick-action-dropdown">
                        <button class="quick-action-btn dropdown-btn" onclick="toggleShiftPerformanceReportDropdown()">
                            📈 Shift Performance Report ▼
                        </button>
                        <div id="shift-performance-dropdown" class="quick-action-dropdown-content" style="display: none;">
                            <a href="#" onclick="handlePrintShiftPerformanceReport(); hideShiftPerformanceReportDropdown();">🖨️ Print Report</a>
                            <a href="#" onclick="handleEmailShiftPerformanceReport(); hideShiftPerformanceReportDropdown();">📧 Email Report</a>
                        </div>
                    </div>

                    <button class="quick-action-btn" onclick="handleAttendance()">
                        👥 Attendance
                    </button>
                    <button class="quick-action-btn" onclick="handleSettings()">
                        ⚙️ Settings
                    </button>
                    <button class="quick-action-btn" onclick="handleEndShift()">
                        🔚 End Shift
                    </button>
                </div>
                <button class="quick-action-close-btn" onclick="closeQuickActionModal()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                    Close
                </button>
            </div>
        </div>
    `;

    // Add click outside to close functionality
    modal.addEventListener('click', function(event) {
        if (event.target === modal) {
            closeQuickActionModal();
        }
    });

    document.body.appendChild(modal);
}

function closeQuickActionModal() {
    const modal = document.getElementById('quick-action-modal');
    if (modal) {
        modal.remove();
    }
}

// Quick Action Handler Functions
function handlePrintDailySaleReport() {
    console.log('Printing Daily Sale Report...');
    // TODO: Implement daily sale report printing
    alert('Daily Sale Report printing functionality will be implemented soon!');
    closeQuickActionModal();
}

async function handleEmailDailySaleReport() {
    console.log('Emailing Daily Sale Report...');
    closeQuickActionModal();

    try {
        // Check if email is configured
        const configResult = await ipcRenderer.invoke('get-email-config');
        if (!configResult.success) {
            alert('Email service not available. Please check configuration.');
            return;
        }

        const config = configResult.data;
        if (!config.apiKey || !config.businessEmail) {
            alert('Email not configured. Please add RESEND_API_KEY and DEFAULT_BUSINESS_EMAIL to .env file.');
            return;
        }

        // Show loading message
        showNotification('📧 Sending daily sales report...', 'info');

        // Send email using default configuration
        const result = await ipcRenderer.invoke('send-daily-report-email');

        if (result.success) {
            showNotification('✅ Daily sales report sent successfully!', 'success');
        } else {
            showNotification(`❌ Failed to send report: ${result.message}`, 'error');
        }
    } catch (error) {
        showNotification(`❌ Error: ${error.message}`, 'error');
    }
}

function handlePrintShiftPerformanceReport() {
    console.log('Printing Shift Performance Report...');
    // TODO: Implement shift performance report printing
    alert('Shift Performance Report printing functionality will be implemented soon!');
    closeQuickActionModal();
}

async function handleEmailShiftPerformanceReport() {
    console.log('Emailing Shift Performance Report...');
    closeQuickActionModal();

    try {
        // Check if email is configured
        const configResult = await ipcRenderer.invoke('get-email-config');
        if (!configResult.success) {
            alert('Email service not available. Please check configuration.');
            return;
        }

        const config = configResult.data;
        if (!config.apiKey || !config.businessEmail) {
            alert('Email not configured. Please add RESEND_API_KEY and DEFAULT_BUSINESS_EMAIL to .env file.');
            return;
        }

        // Show loading message
        showNotification('📧 Sending shift performance report...', 'info');

        // Get current shift ID if available
        const shiftId = window.currentShift?.id || null;

        // Send email using default configuration
        const result = await ipcRenderer.invoke('send-shift-report-email', null, shiftId);

        if (result.success) {
            showNotification('✅ Shift performance report sent successfully!', 'success');
        } else {
            showNotification(`❌ Failed to send report: ${result.message}`, 'error');
        }
    } catch (error) {
        showNotification(`❌ Error: ${error.message}`, 'error');
    }
}

// Dropdown control functions for Quick Action Modal
function toggleDailySaleReportDropdown() {
    const dropdown = document.getElementById('daily-sale-dropdown');
    if (dropdown) {
        dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
    }
    // Hide other dropdowns
    hideShiftPerformanceReportDropdown();
}

function hideDailySaleReportDropdown() {
    const dropdown = document.getElementById('daily-sale-dropdown');
    if (dropdown) {
        dropdown.style.display = 'none';
    }
}

function toggleShiftPerformanceReportDropdown() {
    const dropdown = document.getElementById('shift-performance-dropdown');
    if (dropdown) {
        dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
    }
    // Hide other dropdowns
    hideDailySaleReportDropdown();
}

function hideShiftPerformanceReportDropdown() {
    const dropdown = document.getElementById('shift-performance-dropdown');
    if (dropdown) {
        dropdown.style.display = 'none';
    }
}

function handleAttendance() {
    console.log('Opening Attendance...');
    // TODO: Implement attendance functionality
    alert('Attendance functionality will be implemented soon!');
    closeQuickActionModal();
}

function handleSettings() {
    console.log('Opening Settings...');
    // TODO: Implement settings functionality
    alert('Settings functionality will be implemented soon!');
    closeQuickActionModal();
}

function handleEndShift() {
    console.log('Ending Shift...');
    closeQuickActionModal();
    endCurrentShift();
}

// Shift Management Functions
async function initializeShiftManagement() {
    if (!currentUser) {
        return;
    }

    try {
        const result = await ipcRenderer.invoke('get-current-shift');

        if (result.success && result.shift) {
            currentShift = result.shift;
            showShiftTimer();
            startShiftCountdown();
        } else if (result.success && !result.shift) {
            await startNewShift();
        }
    } catch (error) {
        console.error('Error initializing shift management:', error);
    }
}

async function startNewShift() {
    try {
        const shiftData = {
            duration_hours: 12
        };

        const result = await ipcRenderer.invoke('start-shift', shiftData);

        if (result.success) {
            currentShift = result.shift;
            showShiftTimer();
            startShiftCountdown();
        }
    } catch (error) {
        console.error('Error starting new shift:', error);
    }
}

function showShiftTimer() {
    const container = document.getElementById('shift-timer-container');
    if (container) {
        container.style.display = 'block';
    }
}

function hideShiftTimer() {
    const container = document.getElementById('shift-timer-container');
    if (container) {
        container.style.display = 'none';
    }
}

function startShiftCountdown() {
    // Clear any existing interval
    if (shiftUpdateInterval) {
        clearInterval(shiftUpdateInterval);
    }

    // Update immediately
    updateShiftDisplay();

    // Update every minute
    shiftUpdateInterval = setInterval(updateShiftDisplay, 60000);
}

function updateShiftDisplay() {
    if (!currentShift) {
        return;
    }

    try {
        if (!currentShift.shift_end_time) {
            return;
        }

        const now = dayjs();
        const endTime = dayjs(currentShift.shift_end_time);

        if (!endTime.isValid()) {
            return;
        }

        const remainingMs = endTime.diff(now);
        const remainingMinutes = Math.max(0, Math.floor(remainingMs / (1000 * 60)));

        currentShift.remaining_minutes = remainingMinutes;

        const hours = Math.floor(remainingMinutes / 60);
        const minutes = remainingMinutes % 60;

        if (isNaN(hours) || isNaN(minutes)) {
            return;
        }

        const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`;

        const timerElement = document.getElementById('shift-timer');
        const warningElement = document.getElementById('shift-warning');

        if (timerElement) {
            timerElement.textContent = timeString;

            timerElement.className = 'shift-timer';
            if (remainingMinutes <= 0) {
                timerElement.classList.add('expired');
                timerElement.textContent = 'EXPIRED';
                showShiftEndModal();
            } else if (remainingMinutes <= 30) {
                timerElement.classList.add('warning');
                if (warningElement) {
                    warningElement.style.display = 'block';
                }
            } else {
                if (warningElement) {
                    warningElement.style.display = 'none';
                }
            }
        }

        ipcRenderer.invoke('update-shift-time', remainingMinutes);

    } catch (error) {
        console.error('Error updating shift display:', error);
    }
}

function showShiftEndModal() {
    // Clear the countdown interval
    if (shiftUpdateInterval) {
        clearInterval(shiftUpdateInterval);
    }

    // Show modal asking user to end shift
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
    `;

    modal.innerHTML = `
        <div style="
            background-color: #1a1a1a;
            border: 2px solid #ff0000;
            border-radius: 12px;
            padding: 32px;
            text-align: center;
            color: #ffffff;
            max-width: 500px;
            box-shadow: 0 0 30px rgba(255, 0, 0, 0.5);
        ">
            <h2 style="color: #ff0000; margin-bottom: 16px; font-size: 28px;">⏰ SHIFT EXPIRED</h2>
            <p style="margin-bottom: 24px; font-size: 18px;">Your 12-hour shift has ended. Please end your shift to continue.</p>
            <div style="display: flex; gap: 16px; justify-content: center;">
                <button onclick="endCurrentShift()" style="
                    background-color: #ff0000;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 6px;
                    font-size: 16px;
                    font-weight: bold;
                    cursor: pointer;
                ">End Shift</button>
                <button onclick="extendShift()" style="
                    background-color: #666666;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 6px;
                    font-size: 16px;
                    font-weight: bold;
                    cursor: pointer;
                ">Extend (+1 Hour)</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

async function endCurrentShift() {
    try {
        const shiftSummary = {
            total_sales: await calculateShiftSales(),
            total_transactions: await calculateShiftTransactions()
        };

        const result = await ipcRenderer.invoke('end-shift', shiftSummary);

        if (result.success) {
            currentShift = null;
            hideShiftTimer();

            if (shiftUpdateInterval) {
                clearInterval(shiftUpdateInterval);
            }

            const modal = document.querySelector('div[style*="position: fixed"]');
            if (modal) {
                modal.remove();
            }

            await startNewShift();
        } else {
            alert('Failed to end shift: ' + result.message);
        }
    } catch (error) {
        alert('Error ending shift: ' + error.message);
    }
}

function extendShift() {
    // Extend shift by 1 hour
    if (currentShift) {
        const endTime = new Date(currentShift.shift_end_time);
        endTime.setHours(endTime.getHours() + 1);
        currentShift.shift_end_time = endTime.toISOString();

        // Remove modal
        const modal = document.querySelector('div[style*="position: fixed"]');
        if (modal) {
            modal.remove();
        }

        // Restart countdown
        startShiftCountdown();


    }
}

async function calculateShiftSales() {
    if (!currentShift) {
        return 0;
    }

    try {
        const result = await ipcRenderer.invoke('get-shift-totals', currentShift.shift_id);
        if (result.success) {
            return result.data.total_sales || 0;
        }
    } catch (error) {
        console.error('Error calculating shift sales:', error);
    }
    return 0;
}

async function calculateShiftTransactions() {
    if (!currentShift) {
        return 0;
    }

    try {
        const result = await ipcRenderer.invoke('get-shift-totals', currentShift.shift_id);
        if (result.success) {
            return result.data.total_transactions || 0;
        }
    } catch (error) {
        console.error('Error calculating shift transactions:', error);
    }
    return 0;
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('.quick-action-dropdown')) {
        hideDailySaleReportDropdown();
        hideShiftPerformanceReportDropdown();
    }
});

// Notification system for user feedback
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.pos-notification');
    existingNotifications.forEach(notification => notification.remove());

    const notification = document.createElement('div');
    notification.className = 'pos-notification';

    const bgColor = {
        'success': '#10b981',
        'error': '#ef4444',
        'info': '#3b82f6',
        'warning': '#f59e0b'
    }[type] || '#3b82f6';

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: ${bgColor};
        color: white;
        padding: 16px 24px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        z-index: 20000;
        max-width: 400px;
        word-wrap: break-word;
        animation: slideInRight 0.3s ease-out;
    `;

    notification.textContent = message;
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}

// Add CSS styles for Quick Action Modal dropdowns and notifications
const quickActionStyles = document.createElement('style');
quickActionStyles.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .quick-action-dropdown {
        position: relative;
        display: inline-block;
    }

    .quick-action-dropdown-content {
        position: absolute;
        background-color: white;
        min-width: 200px;
        box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
        border-radius: 8px;
        z-index: 1000;
        border: 2px solid #3b82f6;
        top: 100%;
        left: 0;
        right: 0;
    }

    .quick-action-dropdown-content a {
        color: #374151;
        padding: 14px 18px;
        text-decoration: none;
        display: block;
        font-size: 16px;
        font-weight: 600;
        transition: background-color 0.2s;
        border-radius: 6px;
        margin: 2px;
    }

    .quick-action-dropdown-content a:hover {
        background-color: #f3f4f6;
        color: #1f2937;
    }

    .quick-action-dropdown-content a:first-child {
        border-radius: 6px 6px 0 0;
    }

    .quick-action-dropdown-content a:last-child {
        border-radius: 0 0 6px 6px;
    }

    .dropdown-btn {
        position: relative;
    }

    .dropdown-btn:after {
        content: '';
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid currentColor;
    }
`;
document.head.appendChild(quickActionStyles);
