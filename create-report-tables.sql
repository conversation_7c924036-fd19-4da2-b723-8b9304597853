-- Create Daily Sales Report Table in Supabase
-- This table stores processed daily sales report data for email reporting

CREATE TABLE IF NOT EXISTS daily_sales_reports (
    id BIGSERIAL PRIMARY KEY,
    report_date DATE NOT NULL,
    location_id INTEGER NOT NULL,
    location_name TEXT NOT NULL,
    operator_name TEXT,
    
    -- Sales Summary Data
    total_sales DECIMAL(10,2) DEFAULT 0,
    total_transactions INTEGER DEFAULT 0,
    average_transaction DECIMAL(10,2) DEFAULT 0,
    products_sold INTEGER DEFAULT 0,
    
    -- Payment Method Breakdown
    total_cash DECIMAL(10,2) DEFAULT 0,
    total_debit DECIMAL(10,2) DEFAULT 0,
    total_credit DECIMAL(10,2) DEFAULT 0,
    
    -- Day Shift Data (6 AM - 6 PM)
    day_cash DECIMAL(10,2) DEFAULT 0,
    day_debit DECIMAL(10,2) DEFAULT 0,
    day_credit DECIMAL(10,2) DEFAULT 0,
    day_total DECIMAL(10,2) DEFAULT 0,
    day_transactions INTEGER DEFAULT 0,
    
    -- Night Shift Data (6 PM - 6 AM)
    night_cash DECIMAL(10,2) DEFAULT 0,
    night_debit DECIMAL(10,2) DEFAULT 0,
    night_credit DECIMAL(10,2) DEFAULT 0,
    night_total DECIMAL(10,2) DEFAULT 0,
    night_transactions INTEGER DEFAULT 0,
    
    -- Sale Type Breakdown
    pos_sales DECIMAL(10,2) DEFAULT 0,
    theater_sales DECIMAL(10,2) DEFAULT 0,
    deli_sales DECIMAL(10,2) DEFAULT 0,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    synced_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(report_date, location_id, operator_name)
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_daily_sales_reports_date ON daily_sales_reports(report_date);
CREATE INDEX IF NOT EXISTS idx_daily_sales_reports_location ON daily_sales_reports(location_id);
CREATE INDEX IF NOT EXISTS idx_daily_sales_reports_date_location ON daily_sales_reports(report_date, location_id);

-- Create Shift Sales Report Table in Supabase
-- This table stores processed shift sales report data for email reporting

CREATE TABLE IF NOT EXISTS shift_sales_reports (
    id BIGSERIAL PRIMARY KEY,
    shift_id TEXT NOT NULL UNIQUE,
    shift_date DATE NOT NULL,
    location_id INTEGER NOT NULL,
    location_name TEXT NOT NULL,
    operator_id INTEGER NOT NULL,
    operator_name TEXT NOT NULL,
    
    -- Shift Timing
    shift_start_time TIMESTAMP WITH TIME ZONE,
    shift_end_time TIMESTAMP WITH TIME ZONE,
    shift_duration_hours DECIMAL(4,2) DEFAULT 0,
    shift_type TEXT, -- 'day', 'night', 'full'
    
    -- Sales Summary Data
    total_sales DECIMAL(10,2) DEFAULT 0,
    total_transactions INTEGER DEFAULT 0,
    average_transaction DECIMAL(10,2) DEFAULT 0,
    products_sold INTEGER DEFAULT 0,
    
    -- Payment Method Breakdown
    total_cash DECIMAL(10,2) DEFAULT 0,
    total_debit DECIMAL(10,2) DEFAULT 0,
    total_credit DECIMAL(10,2) DEFAULT 0,
    
    -- Sale Type Breakdown
    pos_sales DECIMAL(10,2) DEFAULT 0,
    theater_sales DECIMAL(10,2) DEFAULT 0,
    deli_sales DECIMAL(10,2) DEFAULT 0,
    
    -- Performance Metrics
    sales_per_hour DECIMAL(10,2) DEFAULT 0,
    transactions_per_hour DECIMAL(6,2) DEFAULT 0,
    
    -- Shift Status
    shift_status TEXT DEFAULT 'active', -- 'active', 'completed', 'extended'
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    synced_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_shift_sales_reports_date ON shift_sales_reports(shift_date);
CREATE INDEX IF NOT EXISTS idx_shift_sales_reports_location ON shift_sales_reports(location_id);
CREATE INDEX IF NOT EXISTS idx_shift_sales_reports_operator ON shift_sales_reports(operator_id);
CREATE INDEX IF NOT EXISTS idx_shift_sales_reports_date_location ON shift_sales_reports(shift_date, location_id);

-- Enable Row Level Security (RLS) for both tables
ALTER TABLE daily_sales_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE shift_sales_reports ENABLE ROW LEVEL SECURITY;

-- Create policies for data access (allow all operations for now)
CREATE POLICY "Allow all operations on daily_sales_reports" ON daily_sales_reports
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on shift_sales_reports" ON shift_sales_reports
    FOR ALL USING (true) WITH CHECK (true);

-- Create functions to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update the updated_at column
CREATE TRIGGER update_daily_sales_reports_updated_at 
    BEFORE UPDATE ON daily_sales_reports 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shift_sales_reports_updated_at 
    BEFORE UPDATE ON shift_sales_reports 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE daily_sales_reports IS 'Processed daily sales report data for email reporting and analytics';
COMMENT ON TABLE shift_sales_reports IS 'Processed shift sales report data for email reporting and analytics';

COMMENT ON COLUMN daily_sales_reports.report_date IS 'Date of the sales report (YYYY-MM-DD)';
COMMENT ON COLUMN daily_sales_reports.total_sales IS 'Total sales amount for the day';
COMMENT ON COLUMN daily_sales_reports.total_transactions IS 'Total number of transactions for the day';

COMMENT ON COLUMN shift_sales_reports.shift_id IS 'Unique identifier for the shift';
COMMENT ON COLUMN shift_sales_reports.shift_duration_hours IS 'Duration of the shift in hours';
COMMENT ON COLUMN shift_sales_reports.sales_per_hour IS 'Sales amount per hour during the shift';
