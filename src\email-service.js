const { Resend } = require('resend');
const ExcelJS = require('exceljs');
const path = require('path');
const fs = require('fs');
const ConfigManager = require('./config');

class EmailService {
    constructor(database) {
        this.db = database;
        this.config = new ConfigManager();
        this.resend = null;
        this.loadConfiguration();
    }

    loadConfiguration() {
        try {
            // Load email configuration from .env file
            const apiKey = this.config.getResendApiKey();
            if (apiKey && apiKey.trim()) {
                this.resend = new Resend(apiKey);
                console.log('📧 Email service initialized with API key from .env file');
                this.config.debugConfig();
            } else {
                console.log('⚠️  No Resend API key found in .env file');
                console.log('📝 Please add RESEND_API_KEY to your .env file');
            }
        } catch (error) {
            console.error('Error loading email configuration:', error);
        }
    }

    setApiKey(apiKey) {
        // Save to .env file
        const success = this.config.setResendApiKey(apiKey);
        if (success) {
            this.resend = new Resend(apiKey);
            console.log('📧 Email API key saved to .env file successfully');
            return true;
        } else {
            console.error('❌ Failed to save email API key to .env file');
            return false;
        }
    }

    setEmailConfig(emailConfig) {
        return this.config.setEmailConfig(emailConfig);
    }

    getEmailConfig() {
        return this.config.getEmailConfig();
    }

    isConfigured() {
        return this.config.isEmailConfigured();
    }

    async generateDailyReportExcel(reportData) {
        const workbook = new ExcelJS.Workbook();
        
        // Sheet 1: Sales Summary
        const summarySheet = workbook.addWorksheet('Sales Summary');
        
        // Add headers
        summarySheet.addRow(['Daily Sales Report']);
        summarySheet.addRow(['Date:', new Date().toLocaleDateString()]);
        summarySheet.addRow(['Location:', reportData.location || 'Rainbow Station Inc.']);
        summarySheet.addRow([]);
        
        // Summary data
        summarySheet.addRow(['Metric', 'Value']);
        summarySheet.addRow(['Total Sales', `$${reportData.totalSales || 0}`]);
        summarySheet.addRow(['Total Transactions', reportData.transactionCount || 0]);
        summarySheet.addRow(['Average Transaction', `$${reportData.averageTransaction || 0}`]);
        summarySheet.addRow(['Products Sold', reportData.productsSold || 0]);
        
        // Sheet 2: Detailed Transactions
        const transactionSheet = workbook.addWorksheet('Transactions');
        transactionSheet.addRow(['Transaction ID', 'Time', 'Amount', 'Payment Method', 'Operator', 'Items']);
        
        if (reportData.transactions) {
            reportData.transactions.forEach(transaction => {
                transactionSheet.addRow([
                    transaction.id,
                    transaction.timestamp,
                    `$${transaction.total}`,
                    transaction.payment_method,
                    transaction.operator_name,
                    transaction.items_count
                ]);
            });
        }

        // Style the sheets
        this.styleExcelSheet(summarySheet);
        this.styleExcelSheet(transactionSheet);

        return workbook;
    }

    async generateShiftReportExcel(shiftData) {
        const workbook = new ExcelJS.Workbook();
        
        // Sheet 1: Shift Overview
        const overviewSheet = workbook.addWorksheet('Shift Overview');
        
        overviewSheet.addRow(['Shift Sales Report']);
        overviewSheet.addRow(['Operator:', shiftData.operator_name || 'Unknown']);
        overviewSheet.addRow(['Shift Start:', shiftData.shift_start || 'N/A']);
        overviewSheet.addRow(['Shift End:', shiftData.shift_end || 'N/A']);
        overviewSheet.addRow(['Location:', shiftData.location || 'Rainbow Station Inc.']);
        overviewSheet.addRow([]);
        
        // Shift summary
        overviewSheet.addRow(['Metric', 'Value']);
        overviewSheet.addRow(['Shift Sales', `$${shiftData.totalSales || 0}`]);
        overviewSheet.addRow(['Transactions', shiftData.transactionCount || 0]);
        overviewSheet.addRow(['Average Transaction', `$${shiftData.averageTransaction || 0}`]);
        overviewSheet.addRow(['Shift Duration', shiftData.duration || 'N/A']);

        // Sheet 2: Shift Transactions
        const transactionSheet = workbook.addWorksheet('Shift Transactions');
        transactionSheet.addRow(['Transaction ID', 'Time', 'Amount', 'Payment Method', 'Items']);
        
        if (shiftData.transactions) {
            shiftData.transactions.forEach(transaction => {
                transactionSheet.addRow([
                    transaction.id,
                    transaction.timestamp,
                    `$${transaction.total}`,
                    transaction.payment_method,
                    transaction.items_count
                ]);
            });
        }

        this.styleExcelSheet(overviewSheet);
        this.styleExcelSheet(transactionSheet);

        return workbook;
    }

    styleExcelSheet(sheet) {
        // Style headers
        sheet.getRow(1).font = { bold: true, size: 16 };
        sheet.getRow(1).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } };
        sheet.getRow(1).font.color = { argb: 'FFFFFFFF' };
        
        // Auto-fit columns
        sheet.columns.forEach(column => {
            column.width = 15;
        });
    }

    async sendDailyReport(recipientEmail = null, reportData) {
        if (!this.resend) {
            throw new Error('Email service not configured. Please add RESEND_API_KEY to .env file.');
        }

        // Use provided email or default from config
        const toEmail = recipientEmail || this.config.getDefaultBusinessEmail();
        if (!toEmail) {
            throw new Error('No recipient email provided and no default business email configured.');
        }

        try {
            // Generate Excel report
            const workbook = await this.generateDailyReportExcel(reportData);
            const buffer = await workbook.xlsx.writeBuffer();

            const senderName = this.config.getDefaultSenderName();

            // Send email
            console.log('📧 Sending daily report email...');
            console.log('   From:', `${senderName} <<EMAIL>>`);
            console.log('   To:', toEmail);
            console.log('   Subject:', `Daily Sales Report - ${new Date().toLocaleDateString()}`);

            const result = await this.resend.emails.send({
                from: `${senderName} <<EMAIL>>`,
                to: [toEmail],
                subject: `Daily Sales Report - ${new Date().toLocaleDateString()}`,
                html: this.generateDailyReportEmailTemplate(reportData),
                attachments: [
                    {
                        filename: `daily-report-${new Date().toISOString().split('T')[0]}.xlsx`,
                        content: buffer.toString('base64'),
                    },
                ],
            });

            console.log('✅ Daily report email sent successfully!');
            console.log('   Email ID:', result.data?.id);
            console.log('   Recipient:', toEmail);
            console.log('⚠️  IMPORTANT: Check spam/junk folder if email not received!');

            return {
                success: true,
                data: result,
                message: `Email sent successfully to ${toEmail}. Check spam folder if not received.`
            };
        } catch (error) {
            console.error('❌ Error sending daily report email:', error);
            return { success: false, error: error.message };
        }
    }

    async sendShiftReport(recipientEmail = null, shiftData) {
        if (!this.resend) {
            throw new Error('Email service not configured. Please add RESEND_API_KEY to .env file.');
        }

        // Use provided email or default from config
        const toEmail = recipientEmail || this.config.getDefaultBusinessEmail();
        if (!toEmail) {
            throw new Error('No recipient email provided and no default business email configured.');
        }

        try {
            // Generate Excel report
            const workbook = await this.generateShiftReportExcel(shiftData);
            const buffer = await workbook.xlsx.writeBuffer();

            const senderName = this.config.getDefaultSenderName();

            // Send email
            const result = await this.resend.emails.send({
                from: `${senderName} <<EMAIL>>`,
                to: [toEmail],
                subject: `Shift Report - ${shiftData.operator_name} - ${new Date().toLocaleDateString()}`,
                html: this.generateShiftReportEmailTemplate(shiftData),
                attachments: [
                    {
                        filename: `shift-report-${shiftData.operator_name}-${new Date().toISOString().split('T')[0]}.xlsx`,
                        content: buffer.toString('base64'),
                    },
                ],
            });

            console.log('📧 Shift report email sent successfully to:', toEmail);
            return { success: true, data: result };
        } catch (error) {
            console.error('❌ Error sending shift report email:', error);
            return { success: false, error: error.message };
        }
    }

    generateDailyReportEmailTemplate(reportData) {
        return `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <div style="background-color: #4472C4; color: white; padding: 20px; text-align: center;">
                    <h1>Daily Sales Report</h1>
                    <p>${new Date().toLocaleDateString()}</p>
                </div>
                
                <div style="padding: 20px; background-color: #f8f9fa;">
                    <h2>Sales Summary</h2>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr style="background-color: #e9ecef;">
                            <td style="padding: 10px; border: 1px solid #dee2e6;"><strong>Total Sales</strong></td>
                            <td style="padding: 10px; border: 1px solid #dee2e6;">$${reportData.totalSales || 0}</td>
                        </tr>
                        <tr>
                            <td style="padding: 10px; border: 1px solid #dee2e6;"><strong>Transactions</strong></td>
                            <td style="padding: 10px; border: 1px solid #dee2e6;">${reportData.transactionCount || 0}</td>
                        </tr>
                        <tr style="background-color: #e9ecef;">
                            <td style="padding: 10px; border: 1px solid #dee2e6;"><strong>Products Sold</strong></td>
                            <td style="padding: 10px; border: 1px solid #dee2e6;">${reportData.productsSold || 0}</td>
                        </tr>
                    </table>
                    
                    <p style="margin-top: 20px;">
                        <strong>Note:</strong> Detailed report is attached as an Excel file.
                    </p>
                </div>
                
                <div style="background-color: #6c757d; color: white; padding: 10px; text-align: center; font-size: 12px;">
                    Rainbow Station Inc. POS System
                </div>
            </div>
        `;
    }

    generateShiftReportEmailTemplate(shiftData) {
        return `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <div style="background-color: #28a745; color: white; padding: 20px; text-align: center;">
                    <h1>Shift Sales Report</h1>
                    <p>Operator: ${shiftData.operator_name || 'Unknown'}</p>
                    <p>${new Date().toLocaleDateString()}</p>
                </div>
                
                <div style="padding: 20px; background-color: #f8f9fa;">
                    <h2>Shift Summary</h2>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr style="background-color: #e9ecef;">
                            <td style="padding: 10px; border: 1px solid #dee2e6;"><strong>Shift Sales</strong></td>
                            <td style="padding: 10px; border: 1px solid #dee2e6;">$${shiftData.totalSales || 0}</td>
                        </tr>
                        <tr>
                            <td style="padding: 10px; border: 1px solid #dee2e6;"><strong>Transactions</strong></td>
                            <td style="padding: 10px; border: 1px solid #dee2e6;">${shiftData.transactionCount || 0}</td>
                        </tr>
                        <tr style="background-color: #e9ecef;">
                            <td style="padding: 10px; border: 1px solid #dee2e6;"><strong>Shift Duration</strong></td>
                            <td style="padding: 10px; border: 1px solid #dee2e6;">${shiftData.duration || 'N/A'}</td>
                        </tr>
                    </table>
                    
                    <p style="margin-top: 20px;">
                        <strong>Note:</strong> Detailed shift report is attached as an Excel file.
                    </p>
                </div>
                
                <div style="background-color: #6c757d; color: white; padding: 10px; text-align: center; font-size: 12px;">
                    Rainbow Station Inc. POS System
                </div>
            </div>
        `;
    }

    async testEmailConfiguration(testEmail = null) {
        if (!this.resend) {
            throw new Error('Email service not configured. Please add RESEND_API_KEY to .env file.');
        }

        // Use provided email or default from config
        const toEmail = testEmail || this.config.getDefaultBusinessEmail();
        if (!toEmail) {
            throw new Error('No test email provided and no default business email configured.');
        }

        try {
            const senderName = this.config.getDefaultSenderName();

            const result = await this.resend.emails.send({
                from: `${senderName} <<EMAIL>>`,
                to: [toEmail],
                subject: 'Test Email - POS System Configuration',
                html: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h1 style="color: #10b981;">✅ Test Email Successful!</h1>
                        <p>Your email configuration is working correctly!</p>
                        <div style="background-color: #f0f9ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
                            <h3>Configuration Details:</h3>
                            <ul>
                                <li><strong>Sender:</strong> ${senderName}</li>
                                <li><strong>Recipient:</strong> ${toEmail}</li>
                                <li><strong>Service:</strong> Resend Email API</li>
                                <li><strong>Status:</strong> Successfully configured</li>
                            </ul>
                        </div>
                        <p style="color: #6b7280; font-size: 14px;">
                            This test email confirms that your Rainbow Station POS system can send emails successfully.
                        </p>
                    </div>
                `,
            });

            console.log('📧 Test email sent successfully to:', toEmail);
            return { success: true, data: result };
        } catch (error) {
            console.error('❌ Error sending test email:', error);
            return { success: false, error: error.message };
        }
    }
}

module.exports = EmailService;
