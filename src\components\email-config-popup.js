// Email Configuration Popup Component
const { ipc<PERSON>ender<PERSON> } = require('electron');

class EmailConfigPopup {
    constructor() {
        this.isVisible = false;
        this.reportType = null; // 'daily' or 'shift'
        this.shiftId = null;
        this.createPopupHTML();
        this.attachEventListeners();
    }

    createPopupHTML() {
        const popupHTML = `
            <div id="email-config-overlay" class="email-popup-overlay" style="display: none;">
                <div class="email-popup-container">
                    <div class="email-popup-header">
                        <h3 id="email-popup-title">Send Report via Email</h3>
                        <button id="email-popup-close" class="email-popup-close">&times;</button>
                    </div>
                    
                    <div class="email-popup-content">
                        <div class="email-form-group">
                            <label for="recipient-email">Business Owner Email *</label>
                            <input type="email" id="recipient-email" placeholder="Enter business owner email address" required>
                        </div>
                        
                        <div class="email-form-group">
                            <label for="resend-api-key">Resend API Key *</label>
                            <input type="password" id="resend-api-key" placeholder="Enter your Resend API key">
                            <small class="email-help-text">
                                Don't have an API key? <a href="#" onclick="openResendSignup()">Sign up at resend.com</a>
                            </small>
                        </div>
                        
                        <div class="email-form-group">
                            <label for="sender-name">Sender Name</label>
                            <input type="text" id="sender-name" placeholder="Rainbow Station POS" value="Rainbow Station POS">
                        </div>
                        
                        <div class="email-form-group">
                            <label>
                                <input type="checkbox" id="save-config"> Save configuration for future use
                            </label>
                        </div>
                        
                        <div class="email-form-group">
                            <button id="test-email-btn" class="email-btn email-btn-secondary">Test Email Configuration</button>
                        </div>
                    </div>
                    
                    <div class="email-popup-footer">
                        <button id="cancel-email-btn" class="email-btn email-btn-cancel">Cancel</button>
                        <button id="send-email-btn" class="email-btn email-btn-primary">Send Report</button>
                    </div>
                    
                    <div id="email-status" class="email-status" style="display: none;"></div>
                </div>
            </div>
        `;

        // Add CSS styles
        const styles = `
            <style>
                .email-popup-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                }
                
                .email-popup-container {
                    background: white;
                    border-radius: 8px;
                    width: 500px;
                    max-width: 90vw;
                    max-height: 90vh;
                    overflow-y: auto;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                }
                
                .email-popup-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 20px;
                    border-bottom: 1px solid #e5e7eb;
                    background-color: #f8f9fa;
                    border-radius: 8px 8px 0 0;
                }
                
                .email-popup-header h3 {
                    margin: 0;
                    color: #1f2937;
                    font-size: 18px;
                    font-weight: 600;
                }
                
                .email-popup-close {
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #6b7280;
                    padding: 0;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .email-popup-close:hover {
                    color: #374151;
                }
                
                .email-popup-content {
                    padding: 20px;
                }
                
                .email-form-group {
                    margin-bottom: 16px;
                }
                
                .email-form-group label {
                    display: block;
                    margin-bottom: 6px;
                    font-weight: 500;
                    color: #374151;
                    font-size: 14px;
                }
                
                .email-form-group input[type="email"],
                .email-form-group input[type="password"],
                .email-form-group input[type="text"] {
                    width: 100%;
                    padding: 10px 12px;
                    border: 1px solid #d1d5db;
                    border-radius: 6px;
                    font-size: 14px;
                    box-sizing: border-box;
                }
                
                .email-form-group input:focus {
                    outline: none;
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }
                
                .email-help-text {
                    display: block;
                    margin-top: 4px;
                    font-size: 12px;
                    color: #6b7280;
                }
                
                .email-help-text a {
                    color: #3b82f6;
                    text-decoration: none;
                }
                
                .email-help-text a:hover {
                    text-decoration: underline;
                }
                
                .email-popup-footer {
                    display: flex;
                    justify-content: flex-end;
                    gap: 12px;
                    padding: 20px;
                    border-top: 1px solid #e5e7eb;
                    background-color: #f8f9fa;
                    border-radius: 0 0 8px 8px;
                }
                
                .email-btn {
                    padding: 10px 20px;
                    border: none;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: background-color 0.2s;
                }
                
                .email-btn-primary {
                    background-color: #3b82f6;
                    color: white;
                }
                
                .email-btn-primary:hover {
                    background-color: #2563eb;
                }
                
                .email-btn-secondary {
                    background-color: #6b7280;
                    color: white;
                    width: 100%;
                }
                
                .email-btn-secondary:hover {
                    background-color: #4b5563;
                }
                
                .email-btn-cancel {
                    background-color: #e5e7eb;
                    color: #374151;
                }
                
                .email-btn-cancel:hover {
                    background-color: #d1d5db;
                }
                
                .email-status {
                    margin: 16px 20px;
                    padding: 12px;
                    border-radius: 6px;
                    font-size: 14px;
                }
                
                .email-status.success {
                    background-color: #ecfdf5;
                    color: #065f46;
                    border: 1px solid #d1fae5;
                }
                
                .email-status.error {
                    background-color: #fef2f2;
                    color: #dc2626;
                    border: 1px solid #fecaca;
                }
                
                .email-status.loading {
                    background-color: #eff6ff;
                    color: #1e40af;
                    border: 1px solid #dbeafe;
                }
            </style>
        `;

        // Add to document
        document.head.insertAdjacentHTML('beforeend', styles);
        document.body.insertAdjacentHTML('beforeend', popupHTML);
    }

    attachEventListeners() {
        // Close popup events
        document.getElementById('email-popup-close').addEventListener('click', () => this.hide());
        document.getElementById('cancel-email-btn').addEventListener('click', () => this.hide());
        document.getElementById('email-config-overlay').addEventListener('click', (e) => {
            if (e.target.id === 'email-config-overlay') {
                this.hide();
            }
        });

        // Test email configuration
        document.getElementById('test-email-btn').addEventListener('click', () => this.testEmailConfiguration());

        // Send email
        document.getElementById('send-email-btn').addEventListener('click', () => this.sendReport());

        // Load saved configuration when popup opens
        this.loadSavedConfiguration();
    }

    async loadSavedConfiguration() {
        try {
            // This would load from database in a real implementation
            // For now, we'll leave it empty and let users enter their config
        } catch (error) {
            console.error('Error loading email configuration:', error);
        }
    }

    show(reportType, shiftId = null) {
        this.reportType = reportType;
        this.shiftId = shiftId;
        
        // Update popup title based on report type
        const title = reportType === 'daily' ? 'Send Daily Sales Report' : 'Send Shift Sales Report';
        document.getElementById('email-popup-title').textContent = title;
        
        // Show popup
        document.getElementById('email-config-overlay').style.display = 'flex';
        this.isVisible = true;
        
        // Focus on email input
        setTimeout(() => {
            document.getElementById('recipient-email').focus();
        }, 100);
    }

    hide() {
        document.getElementById('email-config-overlay').style.display = 'none';
        this.isVisible = false;
        this.hideStatus();
    }

    showStatus(message, type) {
        const statusEl = document.getElementById('email-status');
        statusEl.textContent = message;
        statusEl.className = `email-status ${type}`;
        statusEl.style.display = 'block';
    }

    hideStatus() {
        document.getElementById('email-status').style.display = 'none';
    }

    async testEmailConfiguration() {
        const recipientEmail = document.getElementById('recipient-email').value.trim();
        const apiKey = document.getElementById('resend-api-key').value.trim();

        if (!recipientEmail || !apiKey) {
            this.showStatus('Please enter both email address and API key', 'error');
            return;
        }

        this.showStatus('Testing email configuration...', 'loading');

        try {
            // Set API key first
            await ipcRenderer.invoke('set-email-api-key', apiKey);
            
            // Test configuration
            const result = await ipcRenderer.invoke('test-email-configuration', recipientEmail);
            
            if (result.success) {
                this.showStatus('✅ Email configuration test successful!', 'success');
            } else {
                this.showStatus(`❌ Test failed: ${result.message}`, 'error');
            }
        } catch (error) {
            this.showStatus(`❌ Test failed: ${error.message}`, 'error');
        }
    }

    async sendReport() {
        const recipientEmail = document.getElementById('recipient-email').value.trim();
        const apiKey = document.getElementById('resend-api-key').value.trim();
        const saveConfig = document.getElementById('save-config').checked;

        if (!recipientEmail || !apiKey) {
            this.showStatus('Please enter both email address and API key', 'error');
            return;
        }

        this.showStatus('Sending report...', 'loading');

        try {
            // Set API key
            await ipcRenderer.invoke('set-email-api-key', apiKey);

            // Save configuration if requested
            if (saveConfig) {
                // This would save to database in a real implementation
                console.log('Saving email configuration...');
            }

            // Send appropriate report
            let result;
            if (this.reportType === 'daily') {
                result = await ipcRenderer.invoke('send-daily-report-email', recipientEmail);
            } else if (this.reportType === 'shift') {
                result = await ipcRenderer.invoke('send-shift-report-email', recipientEmail, this.shiftId);
            }

            if (result.success) {
                this.showStatus('✅ Report sent successfully!', 'success');
                setTimeout(() => {
                    this.hide();
                }, 2000);
            } else {
                this.showStatus(`❌ Failed to send report: ${result.message}`, 'error');
            }
        } catch (error) {
            this.showStatus(`❌ Error: ${error.message}`, 'error');
        }
    }
}

// Global function to open Resend signup
window.openResendSignup = function() {
    require('electron').shell.openExternal('https://resend.com/signup');
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EmailConfigPopup;
}

// Global instance for easy access
window.emailConfigPopup = new EmailConfigPopup();
