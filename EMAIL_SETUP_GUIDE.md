# 📧 Email Configuration Guide - Rainbow Station POS

## 🚀 Quick Setup (Recommended)

### **Option 1: Automated Setup Script**

Run the setup script to configure email automatically:

```bash
node setup-email.js
```

The script will guide you through:
1. ✅ Resend API key configuration
2. ✅ Business owner email setup
3. ✅ Sender name customization
4. ✅ Automatic daily reports (optional)
5. ✅ Test email verification

### **Option 2: Manual Configuration**

1. **Get Resend API Key:**
   - Go to [resend.com](https://resend.com/signup)
   - Create free account (3,000 emails/month)
   - Generate API key from dashboard

2. **Edit .env File:**
   ```env
   # Add your configuration to .env file
   RESEND_API_KEY=re_your_api_key_here
   DEFAULT_BUSINESS_EMAIL=<EMAIL>
   DEFAULT_SENDER_NAME=Rainbow Station POS
   AUTO_SEND_DAILY_REPORTS=false
   DAILY_REPORT_TIME=18:00
   ```

3. **Restart Application:**
   ```bash
   npm start
   ```

## 📋 Configuration Options

### **Required Settings:**
- `RESEND_API_KEY` - Your Resend API key (starts with "re_")
- `DEFAULT_BUSINESS_EMAIL` - Where reports will be sent

### **Optional Settings:**
- `DEFAULT_SENDER_NAME` - Email sender name (default: "Rainbow Station POS")
- `AUTO_SEND_DAILY_REPORTS` - Enable automatic daily emails (true/false)
- `DAILY_REPORT_TIME` - Time for automatic reports (HH:MM format)

## 🎯 How to Use Email Reports

### **From POS Quick Actions:**

1. **Daily Sales Report:**
   - Click **Quick Actions** button in POS header
   - Select **📊 Daily Sale Report ▼**
   - Click **📧 Email Report**
   - Report sent automatically to configured email

2. **Shift Performance Report:**
   - Click **Quick Actions** button in POS header
   - Select **📈 Shift Performance Report ▼**
   - Click **📧 Email Report**
   - Report sent automatically to configured email

### **From Admin Panel:**
- Navigate to **Reports** section
- Use dropdown menus on print buttons
- Select **📧 Email Report** option

## 📊 Email Report Contents

### **Daily Sales Report Email:**
- **Subject:** "Daily Sales Report - [Date]"
- **Excel Attachment:** Multi-sheet workbook with:
  - Sales Summary (totals, metrics, payment methods)
  - Detailed Transactions (all sales data)
  - Professional formatting and charts

### **Shift Performance Report Email:**
- **Subject:** "Shift Report - [Operator] - [Date]"
- **Excel Attachment:** Multi-sheet workbook with:
  - Shift Overview (duration, operator details)
  - Shift Transactions (detailed breakdown)
  - Performance metrics and comparisons

## 🔧 Troubleshooting

### **Common Issues:**

1. **"Email not configured" Error:**
   ```
   Solution: Add RESEND_API_KEY and DEFAULT_BUSINESS_EMAIL to .env file
   ```

2. **"Invalid API key" Error:**
   ```
   Solution: Ensure API key starts with "re_" and is from resend.com
   ```

3. **Emails not received:**
   ```
   Solutions:
   - Check spam/junk folder
   - Verify business email address is correct
   - Test with different email address
   - Check Resend dashboard for delivery status
   ```

4. **"Email service not initialized" Error:**
   ```
   Solution: Restart the POS application after configuring .env
   ```

### **Testing Email Configuration:**

```bash
# Run setup script with test option
node setup-email.js

# Or test from POS system:
# Quick Actions → Daily Sale Report → Email Report
```

## 💰 Resend Pricing

### **Free Tier (Perfect for Small Business):**
- ✅ 3,000 emails/month
- ✅ 100 emails/day
- ✅ Full API access
- ✅ Email attachments
- ✅ No domain required

### **Pro Plan ($20/month):**
- ✅ 50,000 emails/month
- ✅ No daily limits
- ✅ Priority support
- ✅ Advanced analytics

## 🔒 Security & Privacy

- ✅ **API keys stored locally** in .env file (not in cloud)
- ✅ **.env file protected** by .gitignore (not committed to git)
- ✅ **Reports generated locally** (no data sent to external servers)
- ✅ **Encrypted email delivery** via HTTPS
- ✅ **No domain hosting required** (uses Resend infrastructure)

## 📁 File Structure

```
rainbow-station-inc-pos/
├── .env                    # Your email configuration (keep private)
├── .env.example           # Template for configuration
├── setup-email.js        # Automated setup script
├── src/
│   ├── config.js         # Configuration manager
│   ├── email-service.js  # Email service implementation
│   └── ...
└── EMAIL_SETUP_GUIDE.md  # This guide
```

## 🎉 Benefits

✅ **Professional Communication** - Automated Excel reports via email
✅ **No Infrastructure Costs** - Works with existing local installation  
✅ **Easy Setup** - One-time configuration with Resend
✅ **Improved Efficiency** - Instant report delivery to business owners
✅ **Professional Branding** - Branded email templates
✅ **Reliable Delivery** - Enterprise-grade email service
✅ **Cost Effective** - Free tier covers most small businesses

## 📞 Support

If you need help:
1. Check this guide first
2. Run the setup script: `node setup-email.js`
3. Verify .env file configuration
4. Test with a simple email address
5. Check Resend dashboard for delivery logs

---

## 🌈 **Ready to Send Professional Reports!**

Your Rainbow Station POS system is now equipped with professional email reporting capabilities. Business owners will receive beautifully formatted Excel reports instantly with just a few clicks from the POS system!
