# Supabase Sales Reports Sync Implementation

## Overview
This document describes the implementation of sales reports sync functionality for the Rainbow Station Inc POS system. The system now automatically generates and syncs aggregated sales report data to Supabase for email reporting and analytics.

## New Database Tables

### Local SQLite Tables
1. **daily_sales_reports** - Aggregated daily sales data by location and operator
2. **shift_sales_reports** - Aggregated shift sales data by operator and shift
3. **daily_sales_report_details** - Individual transaction details for daily reports
4. **shift_sales_report_details** - Individual transaction details for shift reports

### Supabase PostgreSQL Tables
The same tables are mirrored in Supabase with matching schemas for cloud sync.

## Key Features

### Automatic Report Generation
- **On Sale Completion**: Daily sales report data is automatically updated when a sale is completed
- **On Shift End**: Shift sales report data is automatically generated when a shift is ended
- **During Sync**: Reports for the last 30 days are generated before each sync operation

### Data Aggregation
The system aggregates the following metrics:

#### Daily Sales Reports
- Total sales amount and transaction count
- Payment method breakdown (cash, debit, credit)
- Day shift vs night shift breakdown (6 AM - 6 PM vs 6 PM - 6 AM)
- Sale type breakdown (POS, theater, deli)
- Tax information and rates
- Average transaction amounts

#### Shift Sales Reports
- Shift timing and duration
- Sales performance metrics (sales per hour, transactions per hour)
- Payment method and sale type breakdowns
- Operator performance tracking

### Sync Process
1. **Step 1-5**: Sync core data (users, products, sales, etc.)
2. **Step 6**: Generate sales reports data for last 30 days and completed shifts
3. **Step 7**: Sync all sales reports tables to Supabase

## API Endpoints

### IPC Handlers
- `generate-daily-sales-report-data` - Generate daily report data for a specific date
- `generate-shift-sales-report-data` - Generate shift report data for a specific shift
- `send-daily-report-email` - Send daily sales report via email (auto-generates data first)
- `send-shift-report-email` - Send shift performance report via email

### Database Methods
- `generateDailySalesReportData(locationId, reportDate)` - Generate and store daily report
- `generateShiftSalesReportData(shiftId)` - Generate and store shift report
- `storeDailySalesReport(reportData)` - Store daily report data
- `storeShiftSalesReport(reportData)` - Store shift report data

## Schema Alignment

### Fixed Column Mismatches
- **daily_sales_reports**: Changed `day_total`/`night_total` to `day_sales`/`night_sales`
- **shift_sales_reports**: Added both `user_id` and `operator_id` for compatibility
- **Supabase**: Added missing `operator_name` column to daily_sales_reports

### Data Types
- All monetary values use DECIMAL(10,2)
- Timestamps use appropriate timezone handling
- Foreign keys properly reference parent tables

## Email Integration
The sales reports sync integrates with the existing email service:
- Daily reports automatically generate fresh data before sending
- Shift reports use the aggregated data for consistent metrics
- Excel attachments include the processed report data

## Security & Access Control
- Reports respect location-based access control
- Users can only generate reports for their assigned location
- Operator names are tracked for audit purposes

## Performance Considerations
- Reports are generated incrementally (only when needed)
- Batch processing for large date ranges
- Efficient queries with proper indexing
- Error handling for missing data scenarios

## Testing
To test the implementation:
1. Complete a sale in the POS system
2. Check that daily_sales_reports table is updated
3. End a shift
4. Check that shift_sales_reports table is updated
5. Trigger manual sync to verify cloud sync
6. Send email reports to verify data accuracy

## Next Steps
- Monitor sync performance and error rates
- Add more detailed analytics and reporting features
- Implement real-time dashboard updates
- Add automated report scheduling
