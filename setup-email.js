#!/usr/bin/env node

/**
 * Email Configuration Setup Script for Rainbow Station POS
 * 
 * This script helps you configure email settings for your POS system.
 * Run with: node setup-email.js
 */

const readline = require('readline');
const fs = require('fs');
const path = require('path');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validateApiKey(apiKey) {
    return apiKey && apiKey.trim().startsWith('re_') && apiKey.trim().length > 10;
}

async function setupEmailConfiguration() {
    console.log('\n🌈 Rainbow Station POS - Email Configuration Setup');
    console.log('=' .repeat(60));
    console.log('This script will help you configure email settings for your POS system.\n');

    try {
        // Step 1: Get Resend API Key
        console.log('📧 Step 1: Resend API Key Configuration');
        console.log('You need a Resend API key to send emails.');
        console.log('If you don\'t have one, visit: https://resend.com/signup\n');

        let apiKey = '';
        while (!apiKey || !validateApiKey(apiKey)) {
            apiKey = await question('Enter your Resend API key (starts with "re_"): ');
            if (!validateApiKey(apiKey)) {
                console.log('❌ Invalid API key. It should start with "re_" and be longer than 10 characters.\n');
            }
        }

        // Step 2: Get Business Email
        console.log('\n📬 Step 2: Business Owner Email');
        console.log('This is where daily and shift reports will be sent.\n');

        let businessEmail = '';
        while (!businessEmail || !validateEmail(businessEmail)) {
            businessEmail = await question('Enter business owner email address: ');
            if (!validateEmail(businessEmail)) {
                console.log('❌ Invalid email format. Please enter a valid email address.\n');
            }
        }

        // Step 3: Get Sender Name
        console.log('\n🏷️  Step 3: Sender Name (Optional)');
        const senderName = await question('Enter sender name (default: Rainbow Station POS): ') || 'Rainbow Station POS';

        // Step 4: Auto-send Configuration
        console.log('\n⏰ Step 4: Automatic Daily Reports (Optional)');
        const autoSendResponse = await question('Enable automatic daily reports? (y/N): ');
        const autoSend = autoSendResponse.toLowerCase().startsWith('y');

        let reportTime = '18:00';
        if (autoSend) {
            reportTime = await question('What time should daily reports be sent? (HH:MM, default 18:00): ') || '18:00';
        }

        // Step 5: Create .env file
        console.log('\n💾 Step 5: Saving Configuration');
        
        const envContent = [
            '# Rainbow Station POS - Environment Configuration',
            '# Generated by setup-email.js',
            '',
            '# Resend Email Service Configuration',
            `RESEND_API_KEY=${apiKey}`,
            '',
            '# Default Email Settings',
            `DEFAULT_BUSINESS_EMAIL=${businessEmail}`,
            `DEFAULT_SENDER_NAME=${senderName}`,
            '',
            '# Email Configuration',
            `AUTO_SEND_DAILY_REPORTS=${autoSend}`,
            `DAILY_REPORT_TIME=${reportTime}`,
            '',
            '# Application Settings',
            'NODE_ENV=production',
            'DEBUG_MODE=false'
        ].join('\n');

        const envPath = path.join(__dirname, '.env');
        fs.writeFileSync(envPath, envContent, 'utf8');

        // Step 6: Test Configuration
        console.log('\n✅ Configuration saved successfully!');
        console.log('\n🧪 Step 6: Test Email Configuration');
        
        const testResponse = await question('Would you like to send a test email? (Y/n): ');
        if (!testResponse.toLowerCase().startsWith('n')) {
            console.log('\n📧 Sending test email...');
            
            // Import and test email service
            try {
                const ConfigManager = require('./src/config');
                const config = new ConfigManager();
                
                const { Resend } = require('resend');
                const resend = new Resend(apiKey);

                const result = await resend.emails.send({
                    from: `${senderName} <<EMAIL>>`,
                    to: [businessEmail],
                    subject: '✅ Rainbow Station POS - Email Configuration Test',
                    html: `
                        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                            <h1 style="color: #10b981;">🌈 Rainbow Station POS</h1>
                            <h2 style="color: #3b82f6;">Email Configuration Successful!</h2>
                            <p>Congratulations! Your email configuration is working correctly.</p>
                            <div style="background-color: #f0f9ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
                                <h3>Configuration Summary:</h3>
                                <ul>
                                    <li><strong>API Key:</strong> Configured ✅</li>
                                    <li><strong>Business Email:</strong> ${businessEmail}</li>
                                    <li><strong>Sender Name:</strong> ${senderName}</li>
                                    <li><strong>Auto Daily Reports:</strong> ${autoSend ? 'Enabled' : 'Disabled'}</li>
                                    ${autoSend ? `<li><strong>Report Time:</strong> ${reportTime}</li>` : ''}
                                </ul>
                            </div>
                            <p>Your POS system is now ready to send daily sales reports and shift performance reports via email!</p>
                            <p style="color: #6b7280; font-size: 14px;">
                                This test email confirms that your Rainbow Station POS system can send emails successfully.
                            </p>
                        </div>
                    `,
                });

                console.log('✅ Test email sent successfully!');
                console.log(`📬 Check your inbox at: ${businessEmail}`);
            } catch (error) {
                console.log('❌ Test email failed:', error.message);
                console.log('💡 Don\'t worry, your configuration is saved. You can test it later from the POS system.');
            }
        }

        // Final instructions
        console.log('\n🎉 Setup Complete!');
        console.log('=' .repeat(60));
        console.log('Your email configuration has been saved to .env file.');
        console.log('\nNext steps:');
        console.log('1. Restart your POS application');
        console.log('2. Use Quick Actions → Daily Sale Report → Email Report');
        console.log('3. Use Quick Actions → Shift Performance Report → Email Report');
        console.log('\nThe system will automatically use your configured settings.');
        console.log('\n📧 Email reports will be sent to:', businessEmail);
        console.log('🔧 To change settings, edit the .env file or run this script again.');

    } catch (error) {
        console.error('\n❌ Setup failed:', error.message);
        console.log('Please try again or configure manually by editing the .env file.');
    } finally {
        rl.close();
    }
}

// Run the setup
if (require.main === module) {
    setupEmailConfiguration();
}

module.exports = { setupEmailConfiguration };
