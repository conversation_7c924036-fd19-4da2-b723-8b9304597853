#!/usr/bin/env node

/**
 * Email Testing and Diagnostic Tool for Rainbow Station POS
 * 
 * This script helps diagnose email delivery issues.
 * Run with: node test-email.js
 */

const ConfigManager = require('./src/config');
const { Resend } = require('resend');

async function testEmailDelivery() {
    console.log('\n🔍 Rainbow Station POS - Email Diagnostic Tool');
    console.log('=' .repeat(60));

    try {
        // Step 1: Load and validate configuration
        console.log('\n📋 Step 1: Configuration Check');
        const config = new ConfigManager();
        config.debugConfig();

        const emailConfig = config.getEmailConfig();
        const validation = config.validateEmailConfig();

        console.log('\n📧 Email Configuration:');
        console.log('- API Key:', emailConfig.apiKey ? `${emailConfig.apiKey.substring(0, 8)}...` : 'NOT SET');
        console.log('- Business Email:', emailConfig.businessEmail || 'NOT SET');
        console.log('- Sender Name:', emailConfig.senderName);

        if (!validation.isValid) {
            console.log('\n❌ Configuration Issues:');
            validation.errors.forEach(error => console.log(`  - ${error}`));
            return;
        }

        console.log('\n✅ Configuration is valid');

        // Step 2: Test Resend API connection
        console.log('\n🔌 Step 2: Testing Resend API Connection');
        
        if (!emailConfig.apiKey) {
            console.log('❌ No API key found');
            return;
        }

        const resend = new Resend(emailConfig.apiKey);

        // Step 3: Send test email to multiple test addresses
        console.log('\n📧 Step 3: Sending Test Emails');

        const testEmails = [
            {
                to: '<EMAIL>',
                description: 'Resend test address (should always work)'
            },
            {
                to: emailConfig.businessEmail,
                description: 'Your configured business email'
            }
        ];

        for (const testEmail of testEmails) {
            console.log(`\n📤 Sending to: ${testEmail.to} (${testEmail.description})`);
            
            try {
                console.log('📤 API Call Details:');
                console.log('   API Key:', emailConfig.apiKey ? `${emailConfig.apiKey.substring(0, 8)}...` : 'NOT SET');
                console.log('   From:', `${emailConfig.senderName} <<EMAIL>>`);
                console.log('   To:', [testEmail.to]);

                const result = await resend.emails.send({
                    from: `${emailConfig.senderName} <<EMAIL>>`,
                    to: [testEmail.to],
                    subject: `🧪 Test Email - ${new Date().toLocaleString()}`,
                    html: `
                        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                            <h1 style="color: #10b981;">🧪 Email Delivery Test</h1>
                            <p><strong>Test Time:</strong> ${new Date().toLocaleString()}</p>
                            <p><strong>Recipient:</strong> ${testEmail.to}</p>
                            <p><strong>Sender:</strong> ${emailConfig.senderName}</p>
                            
                            <div style="background-color: #f0f9ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
                                <h3>📋 Delivery Checklist:</h3>
                                <ul>
                                    <li>✅ API Key: Configured</li>
                                    <li>✅ Email Service: Resend</li>
                                    <li>✅ Sender: <EMAIL></li>
                                    <li>✅ Format: HTML with attachments support</li>
                                </ul>
                            </div>
                            
                            <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
                                <h3>⚠️ If you don't see this email:</h3>
                                <ol>
                                    <li><strong>Check your spam/junk folder</strong></li>
                                    <li>Add <EMAIL> to your contacts</li>
                                    <li>Check if your email provider blocks automated emails</li>
                                    <li>Try with a different email address</li>
                                </ol>
                            </div>
                            
                            <p style="color: #6b7280; font-size: 14px;">
                                This is a test email from Rainbow Station POS system to verify email delivery.
                            </p>
                        </div>
                    `,
                });

                console.log('📊 Full API Response:', JSON.stringify(result, null, 2));

                if (result.data) {
                    console.log(`✅ Email sent successfully!`);
                    console.log(`   Email ID: ${result.data.id}`);

                    if (testEmail.to === emailConfig.businessEmail) {
                        console.log('\n📬 IMPORTANT: Check your email inbox AND spam folder!');
                        console.log('   Gmail users: Check "Promotions" and "Spam" tabs');
                        console.log('   Outlook users: Check "Junk Email" folder');
                    }
                } else if (result.error) {
                    console.log(`❌ Failed to send: ${result.error.message || 'Unknown error'}`);
                    console.log('📊 Error details:', JSON.stringify(result.error, null, 2));
                } else {
                    console.log(`❌ Unexpected response format`);
                    console.log('📊 Response:', JSON.stringify(result, null, 2));
                }
            } catch (error) {
                console.log(`❌ Error sending email: ${error.message}`);
                
                if (error.message.includes('API key')) {
                    console.log('💡 Tip: Check if your API key is correct and active');
                } else if (error.message.includes('rate limit')) {
                    console.log('💡 Tip: You may have hit the rate limit. Wait a few minutes and try again');
                }
            }
        }

        // Step 4: Provide troubleshooting tips
        console.log('\n🔧 Step 4: Troubleshooting Tips');
        console.log('\nIf emails are not being received:');
        console.log('1. 📁 Check spam/junk folders first');
        console.log('2. 📧 Add <EMAIL> to your email contacts');
        console.log('3. 🔍 Search your email for "Rainbow Station" or "Test Email"');
        console.log('4. 🌐 Try with a different email provider (Gmail, Outlook, etc.)');
        console.log('5. ⏰ Wait 5-10 minutes - some email providers have delays');
        
        console.log('\n📊 Resend Dashboard:');
        console.log('- Visit: https://resend.com/emails');
        console.log('- Check delivery status and logs');
        console.log('- View bounce and complaint reports');

        console.log('\n🎯 Next Steps:');
        console.log('1. Check your email (including spam folder)');
        console.log('2. If still no email, try a different email address');
        console.log('3. Check Resend dashboard for delivery logs');
        console.log('4. Consider upgrading to a custom domain for better deliverability');

    } catch (error) {
        console.error('\n❌ Diagnostic failed:', error.message);
        console.log('\n🔧 Common fixes:');
        console.log('- Ensure .env file exists and has correct format');
        console.log('- Check API key starts with "re_"');
        console.log('- Verify email address format');
        console.log('- Run: npm install resend');
    }
}

// Run the diagnostic
if (require.main === module) {
    testEmailDelivery();
}

module.exports = { testEmailDelivery };
