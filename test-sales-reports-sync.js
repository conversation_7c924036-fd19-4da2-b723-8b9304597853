// Test script for sales reports sync functionality
const Database = require('./src/database');
const { SupabaseSync } = require('./src/supabase-config');

async function testSalesReportsSync() {
  console.log('🧪 Testing Sales Reports Sync Functionality...\n');

  // Initialize database
  const db = new Database();
  db.init();

  // Wait for database to initialize
  await new Promise(resolve => setTimeout(resolve, 2000));

  try {
    // Test 1: Check if tables exist with correct schema
    console.log('📋 Test 1: Checking table schemas...');
    
    const checkTable = (tableName) => {
      return new Promise((resolve, reject) => {
        db.db.all(`PRAGMA table_info(${tableName})`, (err, columns) => {
          if (err) {
            reject(err);
          } else {
            resolve(columns.map(col => col.name));
          }
        });
      });
    };

    const dailyColumns = await checkTable('daily_sales_reports');
    const shiftColumns = await checkTable('shift_sales_reports');

    console.log('Daily Sales Reports columns:', dailyColumns);
    console.log('Shift Sales Reports columns:', shiftColumns);

    // Check for correct column names
    const hasDaySales = dailyColumns.includes('day_sales');
    const hasNightSales = dailyColumns.includes('night_sales');
    const hasUserId = shiftColumns.includes('user_id');

    console.log(`✅ day_sales column: ${hasDaySales ? 'Found' : 'Missing'}`);
    console.log(`✅ night_sales column: ${hasNightSales ? 'Found' : 'Missing'}`);
    console.log(`✅ user_id column: ${hasUserId ? 'Found' : 'Missing'}`);

    // Test 2: Generate sample daily report data
    console.log('\n📊 Test 2: Generating sample daily report data...');
    
    const today = new Date().toISOString().split('T')[0];
    try {
      await db.generateDailySalesReportData(1, today);
      console.log('✅ Daily sales report data generation completed');
    } catch (error) {
      console.log('⚠️ Daily sales report generation (expected if no sales data):', error.message);
    }

    // Test 3: Test Supabase sync
    console.log('\n🔄 Test 3: Testing Supabase sync...');
    
    const supabaseSync = new SupabaseSync(db);
    
    // Test connection
    const connectionTest = await supabaseSync.testConnection();
    console.log('Connection test:', connectionTest.success ? '✅ Success' : '❌ Failed');

    if (connectionTest.success) {
      // Test sync of sales reports tables only
      console.log('Testing sales reports table sync...');
      
      const syncResults = {
        daily_sales_reports: await supabaseSync.syncTable('daily_sales_reports'),
        shift_sales_reports: await supabaseSync.syncTable('shift_sales_reports'),
        daily_sales_report_details: await supabaseSync.syncTable('daily_sales_report_details'),
        shift_sales_report_details: await supabaseSync.syncTable('shift_sales_report_details')
      };

      console.log('\nSync Results:');
      Object.entries(syncResults).forEach(([table, result]) => {
        console.log(`${table}: ${result.success ? '✅ Success' : '❌ Failed'}`);
        if (!result.success) {
          console.log(`  Error: ${result.error}`);
        } else {
          console.log(`  Records synced: ${result.count || 0}`);
        }
      });
    }

    // Test 4: Check data structure
    console.log('\n📋 Test 4: Checking data structure...');
    
    const sampleData = {
      report_date: today,
      location_id: 1,
      location_name: 'Test Location',
      operator_name: 'Test Operator',
      total_sales: 100.00,
      total_transactions: 5,
      day_sales: 60.00,
      night_sales: 40.00
    };

    try {
      await db.storeDailySalesReport(sampleData);
      console.log('✅ Sample data storage test passed');
    } catch (error) {
      console.log('❌ Sample data storage test failed:', error.message);
    }

    console.log('\n🎉 Sales Reports Sync Test Completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    db.close();
  }
}

// Run the test
if (require.main === module) {
  testSalesReportsSync().catch(console.error);
}

module.exports = testSalesReportsSync;
