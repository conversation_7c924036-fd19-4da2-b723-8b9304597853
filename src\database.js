const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');
const path = require('path');

class Database {
  constructor() {
    this.db = null;
  }

  init() {
    // Create database file in app directory
    const dbPath = path.join(__dirname, 'pos_system.db');

    this.db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('Error opening database:', err);
      } else {
        console.log('Connected to SQLite database');
        this.createTables();
        this.createDefaultAdmin();
      }
    });
  }

  createTables() {
    const createUserTable = `
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        role TEXT NOT NULL,
        name TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_login DATETIME,
        status TEXT DEFAULT 'active'
      )
    `;

    const createPermissionsTable = `
      CREATE TABLE IF NOT EXISTS user_permissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        module_id TEXT NOT NULL,
        module_name TEXT NOT NULL,
        can_view INTEGER DEFAULT 0,
        can_edit INTEGER DEFAULT 0,
        can_delete INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        UNIQUE(user_id, module_id)
      )
    `;

    const createProductsTable = `
      CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        barcode TEXT UNIQUE NOT NULL,
        description TEXT NOT NULL,
        category TEXT,
        subcategory TEXT,
        supplier TEXT,
        purchase_price DECIMAL(10,2),
        style TEXT,
        color TEXT,
        size TEXT,
        min_qty INTEGER DEFAULT 0,
        max_qty INTEGER DEFAULT 0,
        image_path TEXT,
        special_discount INTEGER DEFAULT 0,
        priority INTEGER DEFAULT 0,
        image_confirm INTEGER DEFAULT 0,
        non_scanable INTEGER DEFAULT 0,
        daily_item INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    const createLocationStocksTable = `
      CREATE TABLE IF NOT EXISTS location_stocks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        location TEXT NOT NULL,
        stock INTEGER DEFAULT 0,
        price DECIMAL(10,2) DEFAULT 0.00,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
        UNIQUE(product_id, location)
      )
    `;

    const createCategoriesTable = `
      CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        parent_id INTEGER,
        description TEXT,
        display_order INTEGER DEFAULT 0,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES categories (id) ON DELETE SET NULL
      )
    `;

    const createSuppliersTable = `
      CREATE TABLE IF NOT EXISTS suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        address1 TEXT,
        address2 TEXT,
        city TEXT,
        state TEXT,
        zip_code TEXT,
        telephone TEXT,
        fax TEXT,
        email TEXT,
        sales_rep TEXT,
        sales_rep_phone TEXT,
        retail_website TEXT,
        wholesale_website TEXT,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    const createLocationsTable = `
      CREATE TABLE IF NOT EXISTS locations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        location_code TEXT UNIQUE NOT NULL,
        location TEXT NOT NULL,
        company_name TEXT,
        address1 TEXT,
        address2 TEXT,
        phone TEXT,
        tax_percent DECIMAL(5,3) DEFAULT 0.000,
        email TEXT,
        app_mode TEXT,
        theater_plu TEXT,
        theater_time TEXT,
        deli INTEGER DEFAULT 0,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    const createTicketsTable = `
      CREATE TABLE IF NOT EXISTS tickets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        ticket_id TEXT UNIQUE NOT NULL,
        duration INTEGER NOT NULL,
        photo_path TEXT,
        payment_method TEXT NOT NULL,
        ticket_price DECIMAL(10,2) DEFAULT 56.00,
        total_amount DECIMAL(10,2) DEFAULT 56.00,
        user_id INTEGER,
        location_id INTEGER,
        operator_name TEXT,
        location_name TEXT,
        issued_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        status TEXT DEFAULT 'active',
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL,
        FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE SET NULL
      )
    `;

    const createBannedTicketsTable = `
      CREATE TABLE IF NOT EXISTS banned_tickets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        original_ticket_id INTEGER NOT NULL,
        ticket_id TEXT NOT NULL,
        duration INTEGER NOT NULL,
        photo_path TEXT,
        payment_method TEXT NOT NULL,
        user_id INTEGER,
        location_id INTEGER,
        operator_name TEXT,
        location_name TEXT,
        issued_at DATETIME NOT NULL,
        banned_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        banned_by_user_id INTEGER,
        banned_by_operator TEXT,
        ban_reason TEXT NOT NULL,
        action_type TEXT NOT NULL CHECK (action_type IN ('ban', 'grant')),
        is_refunded BOOLEAN DEFAULT 0,
        refund_amount DECIMAL(10,2),
        refunded_at DATETIME,
        refunded_by_user_id INTEGER,
        refunded_by_operator TEXT
      )
    `;

    const createSalesTable = `
      CREATE TABLE IF NOT EXISTS sales (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sale_id TEXT UNIQUE NOT NULL,
        user_id INTEGER,
        location_id INTEGER,
        operator_name TEXT,
        location_name TEXT,
        subtotal DECIMAL(10,2) NOT NULL,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        total_amount DECIMAL(10,2) NOT NULL,
        payment_cash DECIMAL(10,2) DEFAULT 0,
        payment_debit DECIMAL(10,2) DEFAULT 0,
        payment_credit DECIMAL(10,2) DEFAULT 0,
        payment_total DECIMAL(10,2) NOT NULL,
        change_amount DECIMAL(10,2) DEFAULT 0,
        item_count INTEGER NOT NULL,
        sale_type TEXT DEFAULT 'sale',
        sale_date DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        status TEXT DEFAULT 'completed',
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL,
        FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE SET NULL
      )
    `;

    const createSalesItemsTable = `
      CREATE TABLE IF NOT EXISTS sales_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sale_id TEXT NOT NULL,
        product_id INTEGER,
        product_name TEXT NOT NULL,
        product_barcode TEXT,
        product_category TEXT,
        product_subcategory TEXT,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        FOREIGN KEY (sale_id) REFERENCES sales (sale_id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE SET NULL
      )
    `;

    const createDraftSalesTable = `
      CREATE TABLE IF NOT EXISTS draft_sales (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        draft_sale_id TEXT UNIQUE NOT NULL,
        user_id INTEGER,
        location_id INTEGER,
        operator_name TEXT,
        location_name TEXT,
        item_count INTEGER NOT NULL,
        total_amount DECIMAL(10,2) NOT NULL,
        created_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        status TEXT DEFAULT 'draft',
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL,
        FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE SET NULL
      )
    `;

    const createDraftSalesItemsTable = `
      CREATE TABLE IF NOT EXISTS draft_sales_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        draft_sale_id TEXT NOT NULL,
        product_id INTEGER,
        product_name TEXT NOT NULL,
        product_barcode TEXT,
        product_category TEXT,
        product_subcategory TEXT,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        discount DECIMAL(10,2) DEFAULT 0,
        FOREIGN KEY (draft_sale_id) REFERENCES draft_sales (draft_sale_id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE SET NULL
      )
    `;

    const createShiftsTable = `
      CREATE TABLE IF NOT EXISTS shifts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        shift_id TEXT UNIQUE NOT NULL,
        user_id INTEGER NOT NULL,
        location_id INTEGER NOT NULL,
        operator_name TEXT NOT NULL,
        location_name TEXT NOT NULL,
        shift_start_time DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        shift_end_time DATETIME NOT NULL,
        actual_end_time DATETIME,
        shift_duration_hours INTEGER DEFAULT 12,
        remaining_time_minutes INTEGER,
        status TEXT DEFAULT 'active' CHECK (status IN ('active', 'ended', 'expired')),
        total_sales DECIMAL(10,2) DEFAULT 0,
        total_transactions INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        updated_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE CASCADE
      )
    `;

    const createEmailConfigTable = `
      CREATE TABLE IF NOT EXISTS email_configuration (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        resend_api_key TEXT,
        business_owner_email TEXT,
        sender_name TEXT DEFAULT 'Rainbow Station POS',
        auto_send_daily_reports INTEGER DEFAULT 0,
        daily_report_time TEXT DEFAULT '18:00',
        created_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        updated_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime'))
      )
    `;

    const createDailySalesReportsTable = `
      CREATE TABLE IF NOT EXISTS daily_sales_reports (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        report_date DATE NOT NULL,
        location_id INTEGER NOT NULL,
        location_name TEXT NOT NULL,
        operator_name TEXT,

        -- Sales Summary Data
        total_sales DECIMAL(10,2) DEFAULT 0,
        total_transactions INTEGER DEFAULT 0,
        average_transaction DECIMAL(10,2) DEFAULT 0,
        products_sold INTEGER DEFAULT 0,

        -- Payment Method Breakdown
        total_cash DECIMAL(10,2) DEFAULT 0,
        total_debit DECIMAL(10,2) DEFAULT 0,
        total_credit DECIMAL(10,2) DEFAULT 0,

        -- Day Shift Data (6 AM - 6 PM)
        day_cash DECIMAL(10,2) DEFAULT 0,
        day_debit DECIMAL(10,2) DEFAULT 0,
        day_credit DECIMAL(10,2) DEFAULT 0,
        day_sales DECIMAL(10,2) DEFAULT 0,
        day_transactions INTEGER DEFAULT 0,

        -- Night Shift Data (6 PM - 6 AM)
        night_cash DECIMAL(10,2) DEFAULT 0,
        night_debit DECIMAL(10,2) DEFAULT 0,
        night_credit DECIMAL(10,2) DEFAULT 0,
        night_sales DECIMAL(10,2) DEFAULT 0,
        night_transactions INTEGER DEFAULT 0,

        -- Sale Type Breakdown
        pos_sales DECIMAL(10,2) DEFAULT 0,
        pos_transactions INTEGER DEFAULT 0,
        theater_sales DECIMAL(10,2) DEFAULT 0,
        theater_transactions INTEGER DEFAULT 0,
        deli_sales DECIMAL(10,2) DEFAULT 0,
        deli_transactions INTEGER DEFAULT 0,

        -- Tax Information
        subtotal DECIMAL(10,2) DEFAULT 0,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        tax_rate DECIMAL(5,3) DEFAULT 0,

        -- Metadata
        generated_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        updated_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),

        -- Constraints
        UNIQUE(report_date, location_id, operator_name),
        FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE CASCADE
      )
    `;

    const createShiftSalesReportsTable = `
      CREATE TABLE IF NOT EXISTS shift_sales_reports (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        shift_id TEXT NOT NULL UNIQUE,
        shift_date DATE NOT NULL,
        location_id INTEGER NOT NULL,
        location_name TEXT NOT NULL,
        user_id INTEGER NOT NULL,
        operator_id INTEGER NOT NULL,
        operator_name TEXT NOT NULL,

        -- Shift Timing
        shift_start_time DATETIME,
        shift_end_time DATETIME,
        shift_duration_hours DECIMAL(4,2) DEFAULT 0,
        shift_type TEXT, -- 'day', 'night', 'full'

        -- Sales Summary Data
        total_sales DECIMAL(10,2) DEFAULT 0,
        total_transactions INTEGER DEFAULT 0,
        average_transaction DECIMAL(10,2) DEFAULT 0,
        products_sold INTEGER DEFAULT 0,

        -- Payment Method Breakdown
        total_cash DECIMAL(10,2) DEFAULT 0,
        total_debit DECIMAL(10,2) DEFAULT 0,
        total_credit DECIMAL(10,2) DEFAULT 0,

        -- Sale Type Breakdown
        pos_sales DECIMAL(10,2) DEFAULT 0,
        theater_sales DECIMAL(10,2) DEFAULT 0,
        deli_sales DECIMAL(10,2) DEFAULT 0,

        -- Performance Metrics
        sales_per_hour DECIMAL(10,2) DEFAULT 0,
        transactions_per_hour DECIMAL(6,2) DEFAULT 0,

        -- Shift Status
        shift_status TEXT DEFAULT 'active', -- 'active', 'completed', 'extended'

        -- Metadata
        created_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        updated_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
        synced_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),

        FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        FOREIGN KEY (operator_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `;

    const createDailySalesReportDetailsTable = `
      CREATE TABLE IF NOT EXISTS daily_sales_report_details (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        daily_report_id INTEGER NOT NULL,
        sale_id TEXT NOT NULL,
        transaction_time DATETIME NOT NULL,
        operator_name TEXT NOT NULL,
        sale_type TEXT NOT NULL,
        payment_method TEXT NOT NULL,
        shift_type TEXT,
        subtotal DECIMAL(10,2) NOT NULL,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        total_amount DECIMAL(10,2) NOT NULL,
        item_count INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),

        FOREIGN KEY (daily_report_id) REFERENCES daily_sales_reports (id) ON DELETE CASCADE
      )
    `;

    const createShiftSalesReportDetailsTable = `
      CREATE TABLE IF NOT EXISTS shift_sales_report_details (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        shift_report_id INTEGER NOT NULL,
        sale_id TEXT NOT NULL,
        transaction_time DATETIME NOT NULL,
        sale_type TEXT NOT NULL,
        payment_method TEXT NOT NULL,
        subtotal DECIMAL(10,2) NOT NULL,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        total_amount DECIMAL(10,2) NOT NULL,
        item_count INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),

        FOREIGN KEY (shift_report_id) REFERENCES shift_sales_reports (id) ON DELETE CASCADE
      )
    `;

    this.db.run(createUserTable, (err) => {
      if (err) {
        console.error('Error creating users table:', err);
      } else {
        console.log('Users table created successfully');
        // Add name column if it doesn't exist (for existing databases)
        this.addNameColumnIfNotExists();
      }
    });

    this.db.run(createPermissionsTable, (err) => {
      if (err) {
        console.error('Error creating permissions table:', err);
      } else {
        console.log('Permissions table created successfully');
        // Verify table creation
        this.verifyPermissionsTable();
      }
    });

    this.db.run(createProductsTable, (err) => {
      if (err) {
        console.error('Error creating products table:', err);
      } else {
        console.log('Products table created successfully');
      }
    });

    this.db.run(createLocationStocksTable, (err) => {
      if (err) {
        console.error('Error creating location_stocks table:', err);
      } else {
        console.log('Location stocks table created successfully');
      }
    });

    this.db.run(createCategoriesTable, (err) => {
      if (err) {
        console.error('Error creating categories table:', err);
      } else {
        console.log('Categories table created successfully');
      }
    });

    this.db.run(createSuppliersTable, (err) => {
      if (err) {
        console.error('Error creating suppliers table:', err);
      } else {
        console.log('Suppliers table created successfully');
      }
    });

    this.db.run(createLocationsTable, (err) => {
      if (err) {
        console.error('Error creating locations table:', err);
      } else {
        console.log('Locations table created successfully');
      }
    });

    this.db.run(createTicketsTable, (err) => {
      if (err) {
        console.error('Error creating tickets table:', err);
      } else {
        console.log('Tickets table created successfully');
        // Add price columns if they don't exist (for existing databases)
        this.addTicketPriceColumns().catch(err => {
          console.error('Error adding ticket price columns:', err);
        });
      }
    });

    this.db.run(createBannedTicketsTable, (err) => {
      if (err) {
        console.error('Error creating banned_tickets table:', err);
      } else {
        console.log('Banned tickets table created successfully');
      }
    });

    this.db.run(createSalesTable, (err) => {
      if (err) {
        console.error('Error creating sales table:', err);
      } else {
        console.log('Sales table created successfully');
        // Add sale_type column if it doesn't exist (for existing databases)
        this.addSaleTypeColumn().catch(err => {
          console.error('Error adding sale_type column:', err);
        });
      }
    });

    this.db.run(createSalesItemsTable, (err) => {
      if (err) {
        console.error('Error creating sales_items table:', err);
      } else {
        console.log('Sales items table created successfully');
      }
    });

    this.db.run(createDraftSalesTable, (err) => {
      if (err) {
        console.error('Error creating draft_sales table:', err);
      } else {
        console.log('Draft sales table created successfully');
      }
    });

    this.db.run(createDraftSalesItemsTable, (err) => {
      if (err) {
        console.error('Error creating draft_sales_items table:', err);
      } else {
        console.log('Draft sales items table created successfully');
      }
    });

    this.db.run(createShiftsTable, (err) => {
      if (err) {
        console.error('Error creating shifts table:', err);
      } else {
        console.log('Shifts table created successfully');
        // Add sales type columns if they don't exist (for existing databases)
        this.addShiftSalesTypeColumns().catch(err => {
          console.error('Error adding shift sales type columns:', err);
        });
      }
    });

    this.db.run(createEmailConfigTable, (err) => {
      if (err) {
        console.error('Error creating email_configuration table:', err);
      } else {
        console.log('Email configuration table created successfully');
      }
    });

    this.db.run(createDailySalesReportsTable, (err) => {
      if (err) {
        console.error('Error creating daily_sales_reports table:', err);
      } else {
        console.log('Daily sales reports table created successfully');
      }
    });

    this.db.run(createShiftSalesReportsTable, (err) => {
      if (err) {
        console.error('Error creating shift_sales_reports table:', err);
      } else {
        console.log('Shift sales reports table created successfully');
      }
    });

    this.db.run(createDailySalesReportDetailsTable, (err) => {
      if (err) {
        console.error('Error creating daily_sales_report_details table:', err);
      } else {
        console.log('Daily sales report details table created successfully');
      }
    });

    this.db.run(createShiftSalesReportDetailsTable, (err) => {
      if (err) {
        console.error('Error creating shift_sales_report_details table:', err);
      } else {
        console.log('Shift sales report details table created successfully');
      }
    });

    // Run database migrations for existing tables
    setTimeout(() => {
      this.runSalesReportsMigrations();
    }, 1000); // Wait for tables to be created first

    // Add status column to tickets table if it doesn't exist
    this.addStatusColumnIfNotExists();
  }

  addStatusColumnIfNotExists() {
    // Check if status column exists in tickets table, if not add it
    this.db.all("PRAGMA table_info(tickets)", (err, columns) => {
      if (err) {
        console.error('Error checking tickets table structure:', err);
        return;
      }

      const hasStatusColumn = columns.some(col => col.name === 'status');

      if (!hasStatusColumn) {
        this.db.run("ALTER TABLE tickets ADD COLUMN status TEXT DEFAULT 'active'", (err) => {
          if (err) {
            console.error('Error adding status column to tickets table:', err);
          } else {
            console.log('Status column added to tickets table successfully');
            // Update existing tickets to have 'active' status
            this.db.run("UPDATE tickets SET status = 'active' WHERE status IS NULL", (updateErr) => {
              if (updateErr) {
                console.error('Error updating existing tickets with active status:', updateErr);
              } else {
                console.log('Updated existing tickets with active status');
              }
            });
          }
        });
      }
    });
  }

  addNameColumnIfNotExists() {
    // Check if name column exists, if not add it
    this.db.all("PRAGMA table_info(users)", (err, columns) => {
      if (err) {
        console.error('Error checking table structure:', err);
        return;
      }

      const hasNameColumn = columns.some(col => col.name === 'name');
      const hasLastLoginColumn = columns.some(col => col.name === 'last_login');
      const hasStatusColumn = columns.some(col => col.name === 'status');
      const hasLocationIdColumn = columns.some(col => col.name === 'location_id');

      if (!hasNameColumn) {
        this.db.run("ALTER TABLE users ADD COLUMN name TEXT", (err) => {
          if (err) {
            console.error('Error adding name column:', err);
          } else {
            console.log('Name column added successfully');
          }
        });
      }

      if (!hasLastLoginColumn) {
        this.db.run("ALTER TABLE users ADD COLUMN last_login DATETIME", (err) => {
          if (err) {
            console.error('Error adding last_login column:', err);
          } else {
            console.log('Last_login column added successfully');
          }
        });
      }

      if (!hasStatusColumn) {
        this.db.run("ALTER TABLE users ADD COLUMN status TEXT DEFAULT 'active'", (err) => {
          if (err) {
            console.error('Error adding status column:', err);
          } else {
            console.log('Status column added successfully');
          }
        });
      }

      if (!hasLocationIdColumn) {
        this.db.run("ALTER TABLE users ADD COLUMN location_id INTEGER", (err) => {
          if (err) {
            console.error('Error adding location_id column:', err);
          } else {
            console.log('Location ID column added successfully');
          }
        });
      }
    });
  }

  verifyPermissionsTable() {
    // Check if permissions table exists and log its structure
    this.db.all("PRAGMA table_info(user_permissions)", (err, columns) => {
      if (err) {
        console.error('Error checking permissions table structure:', err);
      } else if (columns.length > 0) {
        console.log('Permissions table structure verified:', columns.map(col => col.name));
      } else {
        console.log('Permissions table not found, attempting to create...');
      }
    });

    // Also check if table exists in sqlite_master
    this.db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='user_permissions'", (err, row) => {
      if (err) {
        console.error('Error checking table existence:', err);
      } else if (row) {
        console.log('Permissions table exists in database');
      } else {
        console.log('Permissions table does not exist in database');
      }
    });
  }

  async createDefaultAdmin() {
    // Create default admin user
    const defaultUsername = 'admin';
    const defaultPassword = 'admin123';
    const defaultName = 'System Administrator';
    const hashedPassword = await bcrypt.hash(defaultPassword, 10);

    const insertAdmin = `
      INSERT OR IGNORE INTO users (username, password, role, name, status)
      VALUES (?, ?, 'Admin', ?, 'active')
    `;

    this.db.run(insertAdmin, [defaultUsername, hashedPassword, defaultName], function(err) {
      if (err) {
        console.error('Error creating default admin:', err);
      } else {
        console.log('Default admin user created (username: admin, password: admin123)');
      }
    });

    // Create default cashier user for testing
    const cashierPassword = await bcrypt.hash('cashier123', 10);
    const insertCashier = `
      INSERT OR IGNORE INTO users (username, password, role, name, status)
      VALUES (?, ?, 'Cashier', ?, 'active')
    `;

    this.db.run(insertCashier, ['cashier', cashierPassword, 'Test Cashier'], function(err) {
      if (err) {
        console.error('Error creating default cashier:', err);
      } else {
        console.log('Default cashier user created (username: cashier, password: cashier123)');
      }
    });

    // Create default CCTV user for testing
    const cctvPassword = await bcrypt.hash('cctv123', 10);
    const insertCCTV = `
      INSERT OR IGNORE INTO users (username, password, role, name, status)
      VALUES (?, ?, 'CCTV', ?, 'active')
    `;

    this.db.run(insertCCTV, ['cctv', cctvPassword, 'Test CCTV User'], function(err) {
      if (err) {
        console.error('Error creating default CCTV user:', err);
      } else {
        console.log('Default CCTV user created (username: cctv, password: cctv123)');
      }
    });
  }

  authenticateUser(username, password, role) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT u.*, l.location as location_name, l.location_code, l.company_name, l.theater_time
        FROM users u
        LEFT JOIN locations l ON u.location_id = l.id
        WHERE u.username = ? AND u.role = ? AND u.status = 'active'
      `;

      this.db.get(query, [username, role], async (err, user) => {
        if (err) {
          reject(err);
        } else if (user) {
          // Verify password
          const isValidPassword = await bcrypt.compare(password, user.password);
          if (isValidPassword) {
            // Update last login
            this.updateLastLogin(user.id);
            resolve(user);
          } else {
            resolve(null);
          }
        } else {
          resolve(null);
        }
      });
    });
  }

  updateLastLogin(userId) {
    const query = `UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?`;
    this.db.run(query, [userId], (err) => {
      if (err) {
        console.error('Error updating last login:', err);
      }
    });
  }

  // User Management Functions
  async createUser(userData) {
    const { username, password, role, name, location_id } = userData;
    const hashedPassword = await bcrypt.hash(password, 10);

    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO users (username, password, role, name, location_id, status)
        VALUES (?, ?, ?, ?, ?, 'active')
      `;

      this.db.run(query, [username, hashedPassword, role, name, location_id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, username, role, name, location_id, status: 'active' });
        }
      });
    });
  }

  getAllUsers() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT u.id, u.username, u.role, u.name, u.status, u.created_at, u.last_login,
               u.location_id, l.location as location_name, l.location_code
        FROM users u
        LEFT JOIN locations l ON u.location_id = l.id
        ORDER BY u.created_at DESC
      `;

      this.db.all(query, [], (err, users) => {
        if (err) {
          reject(err);
        } else {
          resolve(users);
        }
      });
    });
  }

  getUserById(id) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT u.id, u.username, u.role, u.name, u.status, u.created_at, u.last_login,
               u.location_id, l.location as location_name, l.location_code
        FROM users u
        LEFT JOIN locations l ON u.location_id = l.id
        WHERE u.id = ?
      `;

      this.db.get(query, [id], (err, user) => {
        if (err) {
          reject(err);
        } else {
          resolve(user);
        }
      });
    });
  }

  async updateUser(id, userData) {
    const { username, role, name, status, location_id } = userData;

    return new Promise((resolve, reject) => {
      const query = `
        UPDATE users
        SET username = ?, role = ?, name = ?, status = ?, location_id = ?
        WHERE id = ?
      `;

      this.db.run(query, [username, role, name, status, location_id, id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  async updateUserPassword(id, newPassword) {
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    return new Promise((resolve, reject) => {
      const query = `UPDATE users SET password = ? WHERE id = ?`;

      this.db.run(query, [hashedPassword, id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  deleteUser(id) {
    return new Promise((resolve, reject) => {
      // First delete user permissions, then delete user
      this.db.run(`DELETE FROM user_permissions WHERE user_id = ?`, [id], (err) => {
        if (err) {
          console.error('Error deleting user permissions:', err);
        }

        // Then delete the user
        const query = `DELETE FROM users WHERE id = ?`;
        this.db.run(query, [id], function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ changes: this.changes });
          }
        });
      });
    });
  }

  // Permission Management Functions
  async createUserPermissions(userId, permissions) {
    return new Promise((resolve, reject) => {
      // First delete existing permissions for this user
      this.db.run(`DELETE FROM user_permissions WHERE user_id = ?`, [userId], (err) => {
        if (err) {
          reject(err);
          return;
        }

        // Insert new permissions
        const insertQuery = `
          INSERT INTO user_permissions (user_id, module_id, module_name, can_view, can_edit, can_delete)
          VALUES (?, ?, ?, ?, ?, ?)
        `;

        let completed = 0;
        let hasError = false;

        if (permissions.length === 0) {
          resolve({ success: true });
          return;
        }

        permissions.forEach(permission => {
          this.db.run(insertQuery, [
            userId,
            permission.module_id,
            permission.module_name,
            permission.can_view ? 1 : 0,
            permission.can_edit ? 1 : 0,
            permission.can_delete ? 1 : 0
          ], (err) => {
            completed++;
            if (err && !hasError) {
              hasError = true;
              reject(err);
            } else if (completed === permissions.length && !hasError) {
              resolve({ success: true });
            }
          });
        });
      });
    });
  }

  getUserPermissions(userId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT module_id, module_name, can_view, can_edit, can_delete
        FROM user_permissions
        WHERE user_id = ?
      `;

      this.db.all(query, [userId], (err, permissions) => {
        if (err) {
          reject(err);
        } else {
          resolve(permissions);
        }
      });
    });
  }

  getUserByUsername(username) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM users WHERE username = ?`;

      this.db.get(query, [username], (err, user) => {
        if (err) {
          reject(err);
        } else {
          resolve(user);
        }
      });
    });
  }

  // Product Management Functions
  async createProduct(productData) {
    const {
      barcode, description, category, subcategory, supplier, purchase_price,
      style, color, size, min_qty, max_qty, image_path,
      special_discount, priority, image_confirm, non_scanable, daily_item
    } = productData;

    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO products (
          barcode, description, category, subcategory, supplier, purchase_price,
          style, color, size, min_qty, max_qty, image_path,
          special_discount, priority, image_confirm, non_scanable, daily_item,
          updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `;

      this.db.run(query, [
        barcode, description, category, subcategory, supplier, purchase_price,
        style, color, size, min_qty || 0, max_qty || 0, image_path,
        special_discount ? 1 : 0, priority ? 1 : 0, image_confirm ? 1 : 0,
        non_scanable ? 1 : 0, daily_item ? 1 : 0
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, barcode, description });
        }
      });
    });
  }

  getAllProducts() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM products
        ORDER BY created_at DESC
      `;

      this.db.all(query, [], (err, products) => {
        if (err) {
          reject(err);
        } else {
          resolve(products);
        }
      });
    });
  }

  // Location-filtered product methods (using purchase_price from products table)
  getProductsByLocation(locationId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT DISTINCT p.*, ls.stock
        FROM products p
        INNER JOIN location_stocks ls ON p.id = ls.product_id
        INNER JOIN locations l ON ls.location = l.location
        WHERE l.id = ?
        ORDER BY p.created_at DESC
      `;

      this.db.all(query, [locationId], (err, products) => {
        if (err) {
          reject(err);
        } else {
          resolve(products);
        }
      });
    });
  }

  getProductsByLocationName(locationName) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT DISTINCT p.*
        FROM products p
        INNER JOIN location_stocks ls ON p.id = ls.product_id
        WHERE ls.location = ?
        ORDER BY p.created_at DESC
      `;

      this.db.all(query, [locationName], (err, products) => {
        if (err) {
          reject(err);
        } else {
          resolve(products);
        }
      });
    });
  }

  getProductById(id) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM products WHERE id = ?`;

      this.db.get(query, [id], (err, product) => {
        if (err) {
          reject(err);
        } else {
          resolve(product);
        }
      });
    });
  }

  getProductByBarcode(barcode) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM products WHERE barcode = ?`;

      this.db.get(query, [barcode], (err, product) => {
        if (err) {
          reject(err);
        } else {
          resolve(product);
        }
      });
    });
  }

  async updateProduct(id, productData) {
    const {
      barcode, description, category, subcategory, supplier, purchase_price,
      style, color, size, min_qty, max_qty, image_path,
      special_discount, priority, image_confirm, non_scanable, daily_item
    } = productData;

    return new Promise((resolve, reject) => {
      const query = `
        UPDATE products SET
          barcode = ?, description = ?, category = ?, subcategory = ?, supplier = ?,
          purchase_price = ?, style = ?, color = ?, size = ?, min_qty = ?, max_qty = ?,
          image_path = ?, special_discount = ?, priority = ?, image_confirm = ?,
          non_scanable = ?, daily_item = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      this.db.run(query, [
        barcode, description, category, subcategory, supplier, purchase_price,
        style, color, size, min_qty || 0, max_qty || 0, image_path,
        special_discount ? 1 : 0, priority ? 1 : 0, image_confirm ? 1 : 0,
        non_scanable ? 1 : 0, daily_item ? 1 : 0, id
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  deleteProduct(id) {
    return new Promise((resolve, reject) => {
      // First delete location stocks, then delete product
      this.db.run(`DELETE FROM location_stocks WHERE product_id = ?`, [id], (err) => {
        if (err) {
          console.error('Error deleting product location stocks:', err);
        }

        // Then delete the product
        const query = `DELETE FROM products WHERE id = ?`;
        this.db.run(query, [id], function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ changes: this.changes });
          }
        });
      });
    });
  }

  // Location Stock Management Functions
  async createLocationStocks(productId, locationStocks) {
    return new Promise((resolve, reject) => {
      // First delete existing location stocks for this product
      this.db.run(`DELETE FROM location_stocks WHERE product_id = ?`, [productId], (err) => {
        if (err) {
          reject(err);
          return;
        }

        // Insert new location stocks
        const insertQuery = `
          INSERT INTO location_stocks (product_id, location, stock, price, updated_at)
          VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
        `;

        let completed = 0;
        let hasError = false;

        if (locationStocks.length === 0) {
          resolve({ success: true });
          return;
        }

        locationStocks.forEach(locationStock => {
          this.db.run(insertQuery, [
            productId,
            locationStock.location,
            locationStock.stock || 0,
            locationStock.price || 0.00
          ], (err) => {
            completed++;
            if (err && !hasError) {
              hasError = true;
              reject(err);
            } else if (completed === locationStocks.length && !hasError) {
              resolve({ success: true });
            }
          });
        });
      });
    });
  }

  getLocationStocksByProductId(productId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT location, stock, price
        FROM location_stocks
        WHERE product_id = ?
        ORDER BY location
      `;

      this.db.all(query, [productId], (err, stocks) => {
        if (err) {
          reject(err);
        } else {
          resolve(stocks);
        }
      });
    });
  }

  async updateLocationStocks(productId, locationStocks) {
    return new Promise((resolve, reject) => {
      // Delete existing stocks and insert new ones
      this.db.run(`DELETE FROM location_stocks WHERE product_id = ?`, [productId], (err) => {
        if (err) {
          reject(err);
          return;
        }

        // Insert updated location stocks
        const insertQuery = `
          INSERT INTO location_stocks (product_id, location, stock, price, updated_at)
          VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
        `;

        let completed = 0;
        let hasError = false;

        if (locationStocks.length === 0) {
          resolve({ success: true });
          return;
        }

        locationStocks.forEach(locationStock => {
          this.db.run(insertQuery, [
            productId,
            locationStock.location,
            locationStock.stock || 0,
            locationStock.price || 0.00
          ], (err) => {
            completed++;
            if (err && !hasError) {
              hasError = true;
              reject(err);
            } else if (completed === locationStocks.length && !hasError) {
              resolve({ success: true });
            }
          });
        });
      });
    });
  }

  // Category Management Functions
  async createCategory(categoryData) {
    const { code, name, parent_id, description, display_order, status } = categoryData;

    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO categories (code, name, parent_id, description, display_order, status, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `;

      this.db.run(query, [
        code, name, parent_id || null, description, display_order || 0, status || 'active'
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, code, name });
        }
      });
    });
  }

  getAllCategories() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT c.*, p.name as parent_name
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        ORDER BY c.display_order, c.name
      `;

      this.db.all(query, [], (err, categories) => {
        if (err) {
          reject(err);
        } else {
          resolve(categories);
        }
      });
    });
  }

  getCategoryById(id) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT c.*, p.name as parent_name
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        WHERE c.id = ?
      `;

      this.db.get(query, [id], (err, category) => {
        if (err) {
          reject(err);
        } else {
          resolve(category);
        }
      });
    });
  }

  getCategoryByCode(code) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT c.*, p.name as parent_name
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        WHERE c.code = ?
      `;

      this.db.get(query, [code], (err, category) => {
        if (err) {
          reject(err);
        } else {
          resolve(category);
        }
      });
    });
  }

  async updateCategory(id, categoryData) {
    const { code, name, parent_id, description, display_order, status } = categoryData;

    return new Promise((resolve, reject) => {
      const query = `
        UPDATE categories SET
          code = ?, name = ?, parent_id = ?, description = ?, display_order = ?,
          status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      this.db.run(query, [
        code, name, parent_id || null, description, display_order || 0, status || 'active', id
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  deleteCategory(id) {
    return new Promise((resolve, reject) => {
      // First check if category has children
      this.db.get(`SELECT COUNT(*) as count FROM categories WHERE parent_id = ?`, [id], (err, result) => {
        if (err) {
          reject(err);
          return;
        }

        if (result.count > 0) {
          reject(new Error('Cannot delete category with subcategories. Please delete subcategories first.'));
          return;
        }

        // Delete the category
        const query = `DELETE FROM categories WHERE id = ?`;
        this.db.run(query, [id], function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ changes: this.changes });
          }
        });
      });
    });
  }

  getParentCategories() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM categories
        WHERE parent_id IS NULL AND status = 'active'
        ORDER BY display_order, name
      `;

      this.db.all(query, [], (err, categories) => {
        if (err) {
          reject(err);
        } else {
          resolve(categories);
        }
      });
    });
  }

  // Supplier Management Functions
  async createSupplier(supplierData) {
    const {
      name, address1, address2, city, state, zip_code, telephone, fax, email,
      sales_rep, sales_rep_phone, retail_website, wholesale_website, status
    } = supplierData;

    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO suppliers (
          name, address1, address2, city, state, zip_code, telephone, fax, email,
          sales_rep, sales_rep_phone, retail_website, wholesale_website, status, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `;

      this.db.run(query, [
        name, address1, address2, city, state, zip_code, telephone, fax, email,
        sales_rep, sales_rep_phone, retail_website, wholesale_website, status || 'active'
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, name });
        }
      });
    });
  }

  getAllSuppliers() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM suppliers
        ORDER BY name
      `;

      this.db.all(query, [], (err, suppliers) => {
        if (err) {
          reject(err);
        } else {
          resolve(suppliers);
        }
      });
    });
  }

  getSupplierById(id) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM suppliers WHERE id = ?`;

      this.db.get(query, [id], (err, supplier) => {
        if (err) {
          reject(err);
        } else {
          resolve(supplier);
        }
      });
    });
  }

  getSupplierByName(name) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM suppliers WHERE name = ?`;

      this.db.get(query, [name], (err, supplier) => {
        if (err) {
          reject(err);
        } else {
          resolve(supplier);
        }
      });
    });
  }

  async updateSupplier(id, supplierData) {
    const {
      name, address1, address2, city, state, zip_code, telephone, fax, email,
      sales_rep, sales_rep_phone, retail_website, wholesale_website, status
    } = supplierData;

    return new Promise((resolve, reject) => {
      const query = `
        UPDATE suppliers SET
          name = ?, address1 = ?, address2 = ?, city = ?, state = ?, zip_code = ?,
          telephone = ?, fax = ?, email = ?, sales_rep = ?, sales_rep_phone = ?,
          retail_website = ?, wholesale_website = ?, status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      this.db.run(query, [
        name, address1, address2, city, state, zip_code, telephone, fax, email,
        sales_rep, sales_rep_phone, retail_website, wholesale_website, status || 'active', id
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  deleteSupplier(id) {
    return new Promise((resolve, reject) => {
      const query = `DELETE FROM suppliers WHERE id = ?`;
      this.db.run(query, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  // Location Management Functions
  async createLocation(locationData) {
    const {
      location_code, location, company_name, address1, address2, phone, tax_percent,
      email, app_mode, theater_plu, theater_time, deli, status
    } = locationData;

    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO locations (
          location_code, location, company_name, address1, address2, phone, tax_percent,
          email, app_mode, theater_plu, theater_time, deli, status, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `;

      this.db.run(query, [
        location_code, location, company_name, address1, address2, phone, tax_percent || 0,
        email, app_mode, theater_plu, theater_time, deli ? 1 : 0, status || 'active'
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, location_code, location });
        }
      });
    });
  }

  getAllLocations() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM locations
        ORDER BY location_code
      `;

      this.db.all(query, [], (err, locations) => {
        if (err) {
          reject(err);
        } else {
          resolve(locations);
        }
      });
    });
  }

  getLocationById(id) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM locations WHERE id = ?`;

      this.db.get(query, [id], (err, location) => {
        if (err) {
          reject(err);
        } else {
          resolve(location);
        }
      });
    });
  }

  getLocationByCode(location_code) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM locations WHERE location_code = ?`;

      this.db.get(query, [location_code], (err, location) => {
        if (err) {
          reject(err);
        } else {
          resolve(location);
        }
      });
    });
  }

  async updateLocation(id, locationData) {
    const {
      location_code, location, company_name, address1, address2, phone, tax_percent,
      email, app_mode, theater_plu, theater_time, deli, status
    } = locationData;

    return new Promise((resolve, reject) => {
      const query = `
        UPDATE locations SET
          location_code = ?, location = ?, company_name = ?, address1 = ?, address2 = ?,
          phone = ?, tax_percent = ?, email = ?, app_mode = ?, theater_plu = ?,
          theater_time = ?, deli = ?, status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      this.db.run(query, [
        location_code, location, company_name, address1, address2, phone, tax_percent || 0,
        email, app_mode, theater_plu, theater_time, deli ? 1 : 0, status || 'active', id
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  deleteLocation(id) {
    return new Promise((resolve, reject) => {
      const query = `DELETE FROM locations WHERE id = ?`;
      this.db.run(query, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  // Force create permissions table if it doesn't exist
  forceCreatePermissionsTable() {
    const createPermissionsTable = `
      CREATE TABLE IF NOT EXISTS user_permissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        module_id TEXT NOT NULL,
        module_name TEXT NOT NULL,
        can_view INTEGER DEFAULT 0,
        can_edit INTEGER DEFAULT 0,
        can_delete INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        UNIQUE(user_id, module_id)
      )
    `;

    return new Promise((resolve, reject) => {
      this.db.run(createPermissionsTable, (err) => {
        if (err) {
          console.error('Error force creating permissions table:', err);
          reject(err);
        } else {
          console.log('Permissions table force created successfully');
          resolve(true);
        }
      });
    });
  }

  // Add sale_type column to sales table if it doesn't exist
  addSaleTypeColumn() {
    return new Promise((resolve, reject) => {
      // Check if column exists
      this.db.all("PRAGMA table_info(sales)", (err, columns) => {
        if (err) {
          reject(err);
          return;
        }

        const hasColumn = columns.some(col => col.name === 'sale_type');
        if (!hasColumn) {
          this.db.run("ALTER TABLE sales ADD COLUMN sale_type TEXT DEFAULT 'sale'", (err) => {
            if (err) {
              console.error('Error adding sale_type column:', err);
              reject(err);
            } else {
              console.log('sale_type column added successfully');
              resolve();
            }
          });
        } else {
          console.log('sale_type column already exists');
          resolve();
        }
      });
    });
  }

  // Add sales type columns to shifts table if they don't exist
  addShiftSalesTypeColumns() {
    return new Promise((resolve, reject) => {
      // Check if columns exist
      this.db.all("PRAGMA table_info(shifts)", (err, columns) => {
        if (err) {
          reject(err);
          return;
        }

        const columnNames = columns.map(col => col.name);
        const columnsToAdd = [
          { name: 'total_sale_sales', type: 'DECIMAL(10,2) DEFAULT 0' },
          { name: 'total_sale_transactions', type: 'INTEGER DEFAULT 0' },
          { name: 'total_theater_sales', type: 'DECIMAL(10,2) DEFAULT 0' },
          { name: 'total_theater_transactions', type: 'INTEGER DEFAULT 0' },
          { name: 'total_deli_sales', type: 'DECIMAL(10,2) DEFAULT 0' },
          { name: 'total_deli_transactions', type: 'INTEGER DEFAULT 0' }
        ];

        const missingColumns = columnsToAdd.filter(col => !columnNames.includes(col.name));

        if (missingColumns.length === 0) {
          console.log('All shift sales type columns already exist');
          resolve();
          return;
        }

        let promises = missingColumns.map(col => {
          return new Promise((res, rej) => {
            this.db.run(`ALTER TABLE shifts ADD COLUMN ${col.name} ${col.type}`, (err) => {
              if (err) {
                console.error(`Error adding ${col.name} column:`, err);
                rej(err);
              } else {
                console.log(`${col.name} column added successfully`);
                res();
              }
            });
          });
        });

        Promise.all(promises).then(() => {
          console.log('All missing shift sales type columns added successfully');
          resolve();
        }).catch(reject);
      });
    });
  }

  // Add price columns to tickets table if they don't exist
  addTicketPriceColumns() {
    return new Promise((resolve, reject) => {
      // Check if columns exist
      this.db.all("PRAGMA table_info(tickets)", (err, columns) => {
        if (err) {
          reject(err);
          return;
        }

        const hasTicketPrice = columns.some(col => col.name === 'ticket_price');
        const hasTotalAmount = columns.some(col => col.name === 'total_amount');

        let promises = [];

        if (!hasTicketPrice) {
          promises.push(new Promise((res, rej) => {
            this.db.run("ALTER TABLE tickets ADD COLUMN ticket_price DECIMAL(10,2) DEFAULT 56.00", (err) => {
              if (err) {
                console.error('Error adding ticket_price column:', err);
                rej(err);
              } else {
                console.log('ticket_price column added successfully');
                res();
              }
            });
          }));
        }

        if (!hasTotalAmount) {
          promises.push(new Promise((res, rej) => {
            this.db.run("ALTER TABLE tickets ADD COLUMN total_amount DECIMAL(10,2) DEFAULT 56.00", (err) => {
              if (err) {
                console.error('Error adding total_amount column to tickets:', err);
                rej(err);
              } else {
                console.log('total_amount column added to tickets successfully');
                res();
              }
            });
          }));
        }

        if (promises.length === 0) {
          console.log('Ticket price columns already exist');
          resolve();
        } else {
          Promise.all(promises).then(() => {
            // Update existing tickets with default price
            this.db.run(`
              UPDATE tickets
              SET ticket_price = 56.00, total_amount = 56.00
              WHERE ticket_price IS NULL OR total_amount IS NULL
            `, (err) => {
              if (err) {
                console.error('Error updating existing tickets with default price:', err);
                reject(err);
              } else {
                console.log('Existing tickets updated with default price');
                resolve();
              }
            });
          }).catch(reject);
        }
      });
    });
  }

  // Ticket management methods
  createTicket(ticketData) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        console.error('Database connection not available');
        reject(new Error('Database connection not available'));
        return;
      }

      // Calculate ticket price (default $56.00 per ticket regardless of duration)
      const ticketPrice = ticketData.ticket_price || 56.00;
      const totalAmount = ticketPrice; // For now, total equals ticket price

      const query = `
        INSERT INTO tickets (
          ticket_id, duration, photo_path, payment_method,
          ticket_price, total_amount,
          user_id, location_id, operator_name, location_name,
          issued_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime(CURRENT_TIMESTAMP, 'localtime'))
      `;

      const values = [
        ticketData.ticket_id,
        ticketData.duration,
        ticketData.photo_path,
        ticketData.payment_method,
        ticketPrice,
        totalAmount,
        ticketData.user_id,
        ticketData.location_id,
        ticketData.operator_name,
        ticketData.location_name
      ];

      this.db.run(query, values, function(err) {
        if (err) {
          console.error('Error creating ticket:', err);
          reject(err);
        } else {
          console.log('Ticket created successfully with ID:', this.lastID);
          resolve({
            id: this.lastID,
            ticket_id: ticketData.ticket_id,
            success: true
          });
        }
      });
    });
  }

  getAllTickets() {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        console.error('Database connection not available for getAllTickets');
        reject(new Error('Database connection not available'));
        return;
      }

      const query = `
        SELECT t.*, u.username, l.location_code
        FROM tickets t
        LEFT JOIN users u ON t.user_id = u.id
        LEFT JOIN locations l ON t.location_id = l.id
        ORDER BY t.issued_at DESC
      `;

      this.db.all(query, [], (err, rows) => {
        if (err) {
          console.error('Error fetching tickets:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // Location-filtered ticket methods
  getTicketsByLocation(locationId) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        console.error('Database connection not available for getTicketsByLocation');
        reject(new Error('Database connection not available'));
        return;
      }

      const query = `
        SELECT t.*, u.username, l.location_code
        FROM tickets t
        LEFT JOIN users u ON t.user_id = u.id
        LEFT JOIN locations l ON t.location_id = l.id
        WHERE t.location_id = ?
        ORDER BY t.issued_at DESC
      `;

      this.db.all(query, [locationId], (err, rows) => {
        if (err) {
          console.error('Error fetching tickets by location:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  getTicketById(ticketId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT t.*, u.username, l.location_code
        FROM tickets t
        LEFT JOIN users u ON t.user_id = u.id
        LEFT JOIN locations l ON t.location_id = l.id
        WHERE t.ticket_id = ?
      `;

      this.db.get(query, [ticketId], (err, row) => {
        if (err) {
          console.error('Error fetching ticket:', err);
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // Banned ticket methods
  banTicket(ticketId, banData) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database connection not available'));
        return;
      }

      const db = this.db; // Store reference to avoid context issues

      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        // First get the original ticket data
        db.get('SELECT * FROM tickets WHERE id = ?', [ticketId], (err, ticket) => {
          if (err) {
            db.run('ROLLBACK');
            reject(err);
            return;
          }

          if (!ticket) {
            db.run('ROLLBACK');
            reject(new Error('Ticket not found'));
            return;
          }

          // Insert into banned_tickets table
          const query = `
            INSERT INTO banned_tickets (
              original_ticket_id, ticket_id, duration, photo_path, payment_method,
              user_id, location_id, operator_name, location_name, issued_at,
              banned_by_user_id, banned_by_operator, ban_reason, action_type
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `;

          const values = [
            ticket.id, ticket.ticket_id, ticket.duration, ticket.photo_path,
            ticket.payment_method, ticket.user_id, ticket.location_id,
            ticket.operator_name, ticket.location_name, ticket.issued_at,
            banData.banned_by_user_id, banData.banned_by_operator,
            banData.ban_reason, banData.action_type
          ];

          db.run(query, values, function(err) {
            if (err) {
              db.run('ROLLBACK');
              reject(err);
              return;
            }

            const bannedTicketId = this.lastID;

            // Update original ticket status to 'banned'
            db.run('UPDATE tickets SET status = ? WHERE id = ?', ['banned', ticketId], (err) => {
              if (err) {
                db.run('ROLLBACK');
                reject(err);
                return;
              }

              db.run('COMMIT', (err) => {
                if (err) {
                  reject(err);
                } else {
                  resolve({ id: bannedTicketId, original_ticket_id: ticketId });
                }
              });
            });
          });
        });
      });
    });
  }

  getBannedTickets() {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database connection not available'));
        return;
      }

      const query = `
        SELECT bt.*,
               u.username as banned_by_username,
               ru.username as refunded_by_username
        FROM banned_tickets bt
        LEFT JOIN users u ON bt.banned_by_user_id = u.id
        LEFT JOIN users ru ON bt.refunded_by_user_id = ru.id
        ORDER BY bt.banned_at DESC
      `;

      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // Location-filtered banned tickets method
  getBannedTicketsByLocation(locationId) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database connection not available'));
        return;
      }

      const query = `
        SELECT bt.*,
               u.username as banned_by_username,
               ru.username as refunded_by_username
        FROM banned_tickets bt
        LEFT JOIN users u ON bt.banned_by_user_id = u.id
        LEFT JOIN users ru ON bt.refunded_by_user_id = ru.id
        WHERE bt.location_id = ?
        ORDER BY bt.banned_at DESC
      `;

      this.db.all(query, [locationId], (err, rows) => {
        if (err) {
          console.error('Error fetching banned tickets by location:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  refundBannedTicket(bannedTicketId, refundData) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database connection not available'));
        return;
      }

      const db = this.db; // Store reference to avoid context issues

      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        // First get the banned ticket to find the original ticket ID
        db.get('SELECT original_ticket_id FROM banned_tickets WHERE id = ?', [bannedTicketId], (err, bannedTicket) => {
          if (err) {
            db.run('ROLLBACK');
            reject(err);
            return;
          }

          if (!bannedTicket) {
            db.run('ROLLBACK');
            reject(new Error('Banned ticket not found'));
            return;
          }

          // Update the banned_tickets table with refund information
          const updateBannedQuery = `
            UPDATE banned_tickets
            SET is_refunded = 1,
                refund_amount = ?,
                refunded_at = datetime(CURRENT_TIMESTAMP, 'localtime'),
                refunded_by_user_id = ?,
                refunded_by_operator = ?
            WHERE id = ?
          `;

          const bannedValues = [
            refundData.refund_amount,
            refundData.refunded_by_user_id,
            refundData.refunded_by_operator,
            bannedTicketId
          ];

          db.run(updateBannedQuery, bannedValues, function(err) {
            if (err) {
              db.run('ROLLBACK');
              reject(err);
              return;
            }

            // Update the original tickets table status to 'refunded'
            db.run('UPDATE tickets SET status = ? WHERE id = ?', ['refunded', bannedTicket.original_ticket_id], (err) => {
              if (err) {
                db.run('ROLLBACK');
                reject(err);
                return;
              }

              db.run('COMMIT', (err) => {
                if (err) {
                  reject(err);
                } else {
                  resolve({
                    id: bannedTicketId,
                    original_ticket_id: bannedTicket.original_ticket_id,
                    changes: this.changes
                  });
                }
              });
            });
          });
        });
      });
    });
  }

  // Draft Sales Management Functions
  async createDraftSale(draftData) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database connection not available'));
        return;
      }

      const db = this.db;
      const {
        draftSaleId, userId, locationId, operatorName, locationName,
        itemCount, totalAmount, items
      } = draftData;

      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        // Insert main draft sale record
        const draftQuery = `
          INSERT INTO draft_sales (
            draft_sale_id, user_id, location_id, operator_name, location_name,
            item_count, total_amount, status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, 'draft')
        `;

        db.run(draftQuery, [
          draftSaleId, userId, locationId, operatorName, locationName,
          itemCount, totalAmount
        ], function(err) {
          if (err) {
            db.run('ROLLBACK');
            reject(err);
            return;
          }

          // Insert draft sale items
          const itemQuery = `
            INSERT INTO draft_sales_items (
              draft_sale_id, product_id, product_name, product_barcode,
              product_category, product_subcategory, quantity, unit_price, total_price, discount
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `;

          let itemsProcessed = 0;
          let hasError = false;

          if (items.length === 0) {
            db.run('COMMIT');
            resolve({ draftSaleId, id: this.lastID });
            return;
          }

          items.forEach(item => {
            db.run(itemQuery, [
              draftSaleId, item.productId, item.name, item.barcode,
              item.category, item.subcategory, item.quantity, item.price, item.total, item.discount || 0
            ], function(err) {
              if (err && !hasError) {
                hasError = true;
                db.run('ROLLBACK');
                reject(err);
                return;
              }

              itemsProcessed++;
              if (itemsProcessed === items.length && !hasError) {
                db.run('COMMIT');
                resolve({ draftSaleId, id: this.lastID });
              }
            });
          });
        });
      });
    });
  }

  // Get draft sales by location (for location-based access control)
  getDraftSalesByLocation(locationId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT ds.*, COUNT(dsi.id) as item_count_actual
        FROM draft_sales ds
        LEFT JOIN draft_sales_items dsi ON ds.draft_sale_id = dsi.draft_sale_id
        WHERE ds.location_id = ? AND ds.status = 'draft'
        GROUP BY ds.id
        ORDER BY ds.created_at DESC
      `;

      this.db.all(query, [locationId], (err, drafts) => {
        if (err) {
          reject(err);
        } else {
          resolve(drafts);
        }
      });
    });
  }

  // Get all draft sales (admin only)
  getAllDraftSales() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT ds.*, COUNT(dsi.id) as item_count_actual
        FROM draft_sales ds
        LEFT JOIN draft_sales_items dsi ON ds.draft_sale_id = dsi.draft_sale_id
        WHERE ds.status = 'draft'
        GROUP BY ds.id
        ORDER BY ds.created_at DESC
      `;

      this.db.all(query, [], (err, drafts) => {
        if (err) {
          reject(err);
        } else {
          resolve(drafts);
        }
      });
    });
  }

  // Get draft sale details with items
  getDraftSaleDetails(draftSaleId) {
    return new Promise((resolve, reject) => {
      const draftQuery = `SELECT * FROM draft_sales WHERE draft_sale_id = ? AND status = 'draft'`;
      const itemsQuery = `SELECT * FROM draft_sales_items WHERE draft_sale_id = ?`;

      this.db.get(draftQuery, [draftSaleId], (err, draft) => {
        if (err) {
          reject(err);
          return;
        }

        if (!draft) {
          resolve(null);
          return;
        }

        this.db.all(itemsQuery, [draftSaleId], (err, items) => {
          if (err) {
            reject(err);
          } else {
            resolve({ ...draft, items });
          }
        });
      });
    });
  }

  // Delete draft sale
  deleteDraftSale(draftSaleId) {
    return new Promise((resolve, reject) => {
      const query = `DELETE FROM draft_sales WHERE draft_sale_id = ?`;

      this.db.run(query, [draftSaleId], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  // Sales Management Functions
  async createSale(saleData) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database connection not available'));
        return;
      }

      const db = this.db;
      const self = this; // Store reference to Database instance
      const {
        saleId, userId, locationId, operatorName, locationName,
        subtotal, taxAmount, discountAmount, totalAmount,
        paymentCash, paymentDebit, paymentCredit, paymentTotal,
        changeAmount, itemCount, items
      } = saleData;

      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        // Determine sale type based on items
        let saleType = 'sale';
        if (items && items.length > 0) {
          // Check if any item is a daily item (deli)
          const hasDeliItems = items.some(item => item.isDeliItem);
          console.log('🔍 DELI DEBUG - Sale creation analysis:');
          console.log(`  Total items: ${items.length}`);
          console.log(`  Items with isDeliItem flag:`, items.filter(item => item.isDeliItem));
          console.log(`  Has deli items: ${hasDeliItems}`);

          if (hasDeliItems) {
            saleType = 'deli';
            console.log(`✅ DELI: Sale marked as 'deli' type`);
          } else {
            console.log(`📦 REGULAR: Sale marked as 'sale' type`);
          }
        }

        // Insert main sale record
        const saleQuery = `
          INSERT INTO sales (
            sale_id, user_id, location_id, operator_name, location_name,
            subtotal, tax_amount, discount_amount, total_amount,
            payment_cash, payment_debit, payment_credit, payment_total,
            change_amount, item_count, sale_type, status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'completed')
        `;

        db.run(saleQuery, [
          saleId, userId, locationId, operatorName, locationName,
          subtotal, taxAmount, discountAmount, totalAmount,
          paymentCash, paymentDebit, paymentCredit, paymentTotal,
          changeAmount, itemCount, saleType
        ], function(err) {
          if (err) {
            db.run('ROLLBACK');
            reject(err);
            return;
          }

          // Insert sale items
          const itemQuery = `
            INSERT INTO sales_items (
              sale_id, product_id, product_name, product_barcode,
              product_category, product_subcategory, quantity, unit_price, total_price
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          `;

          let itemsProcessed = 0;
          let hasError = false;

          if (items.length === 0) {
            db.run('COMMIT');

            // Update shift totals after successful sale
            self.updateShiftTotalsForSale(userId, locationId, totalAmount, saleType)
              .then(() => {
                console.log(`✅ Sale ${saleId} completed and shift totals updated`);
                resolve({ saleId, id: this.lastID });
              })
              .catch(shiftErr => {
                console.error('Error updating shift totals:', shiftErr);
                // Don't fail the sale if shift update fails
                resolve({ saleId, id: this.lastID });
              });
            return;
          }

          items.forEach(item => {
            db.run(itemQuery, [
              saleId, item.productId, item.name, item.barcode,
              item.category, item.subcategory, item.quantity, item.price, item.total
            ], function(err) {
              if (err && !hasError) {
                hasError = true;
                db.run('ROLLBACK');
                reject(err);
                return;
              }

              itemsProcessed++;
              if (itemsProcessed === items.length && !hasError) {
                db.run('COMMIT');

                // Update shift totals after successful sale
                self.updateShiftTotalsForSale(userId, locationId, totalAmount, saleType)
                  .then(() => {
                    console.log(`✅ Sale ${saleId} completed and shift totals updated`);
                    resolve({ saleId, id: this.lastID });
                  })
                  .catch(shiftErr => {
                    console.error('Error updating shift totals:', shiftErr);
                    // Don't fail the sale if shift update fails
                    resolve({ saleId, id: this.lastID });
                  });
              }
            });
          });
        });
      });
    });
  }

  // Get sales by location (for location-based access control)
  getSalesByLocation(locationId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT s.*, COUNT(si.id) as item_count_actual
        FROM sales s
        LEFT JOIN sales_items si ON s.sale_id = si.sale_id
        WHERE s.location_id = ?
        GROUP BY s.id
        ORDER BY s.sale_date DESC
      `;

      this.db.all(query, [locationId], (err, sales) => {
        if (err) {
          reject(err);
        } else {
          resolve(sales);
        }
      });
    });
  }

  // Daily Sales Report Methods
  getDailySalesReport(locationId, dateFrom, dateTo, shift = 'both') {
    return new Promise((resolve, reject) => {
      let timeCondition = '';

      // Add shift filtering based on time
      if (shift === 'day') {
        timeCondition = " AND strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18'";
      } else if (shift === 'night') {
        timeCondition = " AND (strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06')";
      }

      const query = `
        SELECT
          DATE(s.sale_date) as invoice_date,
          'sale' as sale_type,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18' THEN s.payment_cash ELSE 0 END) as day_cash,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18' THEN s.payment_debit ELSE 0 END) as day_debit,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18' THEN s.payment_credit ELSE 0 END) as day_credit,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18' THEN s.total_amount ELSE 0 END) as day_total,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06' THEN s.payment_cash ELSE 0 END) as night_cash,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06' THEN s.payment_debit ELSE 0 END) as night_debit,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06' THEN s.payment_credit ELSE 0 END) as night_credit,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06' THEN s.total_amount ELSE 0 END) as night_total,
          SUM(s.payment_cash) as total_cash,
          SUM(s.payment_debit) as total_debit,
          SUM(s.payment_credit) as total_credit,
          SUM(s.total_amount) as total_amount,
          s.location_name,
          s.operator_name
        FROM sales s
        WHERE s.location_id = ?
          AND DATE(s.sale_date) >= ?
          AND DATE(s.sale_date) <= ?
          AND (s.sale_type = 'sale' OR s.sale_type IS NULL)
          ${timeCondition}
        GROUP BY DATE(s.sale_date), s.location_name, s.operator_name
        ORDER BY DATE(s.sale_date) DESC
      `;

      this.db.all(query, [locationId, dateFrom, dateTo], (err, sales) => {
        if (err) {
          reject(err);
        } else {
          resolve(sales);
        }
      });
    });
  }

  getTheaterSalesReport(locationId, dateFrom, dateTo, shift = 'both') {
    return new Promise((resolve, reject) => {
      let timeCondition = '';

      if (shift === 'day') {
        timeCondition = " AND strftime('%H', t.issued_at) >= '06' AND strftime('%H', t.issued_at) < '18'";
      } else if (shift === 'night') {
        timeCondition = " AND (strftime('%H', t.issued_at) >= '18' OR strftime('%H', t.issued_at) < '06')";
      }

      const query = `
        SELECT
          DATE(t.issued_at) as invoice_date,
          'theater' as sale_type,
          SUM(CASE WHEN strftime('%H', t.issued_at) >= '06' AND strftime('%H', t.issued_at) < '18'
                   AND t.payment_method = 'cash' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as day_cash,
          SUM(CASE WHEN strftime('%H', t.issued_at) >= '06' AND strftime('%H', t.issued_at) < '18'
                   AND t.payment_method = 'debit' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as day_debit,
          SUM(CASE WHEN strftime('%H', t.issued_at) >= '06' AND strftime('%H', t.issued_at) < '18'
                   AND t.payment_method = 'credit' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as day_credit,
          SUM(CASE WHEN strftime('%H', t.issued_at) >= '06' AND strftime('%H', t.issued_at) < '18'
                   THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as day_total,
          SUM(CASE WHEN (strftime('%H', t.issued_at) >= '18' OR strftime('%H', t.issued_at) < '06')
                   AND t.payment_method = 'cash' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as night_cash,
          SUM(CASE WHEN (strftime('%H', t.issued_at) >= '18' OR strftime('%H', t.issued_at) < '06')
                   AND t.payment_method = 'debit' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as night_debit,
          SUM(CASE WHEN (strftime('%H', t.issued_at) >= '18' OR strftime('%H', t.issued_at) < '06')
                   AND t.payment_method = 'credit' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as night_credit,
          SUM(CASE WHEN (strftime('%H', t.issued_at) >= '18' OR strftime('%H', t.issued_at) < '06')
                   THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as night_total,
          SUM(CASE WHEN t.payment_method = 'cash' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as total_cash,
          SUM(CASE WHEN t.payment_method = 'debit' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as total_debit,
          SUM(CASE WHEN t.payment_method = 'credit' THEN COALESCE(t.total_amount, 56.00) ELSE 0 END) as total_credit,
          SUM(COALESCE(t.total_amount, 56.00)) as total_amount,
          t.location_name,
          t.operator_name
        FROM tickets t
        WHERE t.location_id = ?
          AND DATE(t.issued_at) >= ?
          AND DATE(t.issued_at) <= ?
          AND t.status = 'active'
          ${timeCondition}
        GROUP BY DATE(t.issued_at), t.location_name, t.operator_name
        ORDER BY DATE(t.issued_at) DESC
      `;

      this.db.all(query, [locationId, dateFrom, dateTo], (err, tickets) => {
        if (err) {
          reject(err);
        } else {
          resolve(tickets);
        }
      });
    });
  }

  getDeliSalesReport(locationId, dateFrom, dateTo, shift = 'both') {
    return new Promise((resolve, reject) => {
      let timeCondition = '';

      if (shift === 'day') {
        timeCondition = " AND strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18'";
      } else if (shift === 'night') {
        timeCondition = " AND (strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06')";
      }

      const query = `
        SELECT
          DATE(s.sale_date) as invoice_date,
          'deli' as sale_type,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18' THEN s.payment_cash ELSE 0 END) as day_cash,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18' THEN s.payment_debit ELSE 0 END) as day_debit,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18' THEN s.payment_credit ELSE 0 END) as day_credit,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '06' AND strftime('%H', s.sale_date) < '18' THEN s.total_amount ELSE 0 END) as day_total,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06' THEN s.payment_cash ELSE 0 END) as night_cash,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06' THEN s.payment_debit ELSE 0 END) as night_debit,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06' THEN s.payment_credit ELSE 0 END) as night_credit,
          SUM(CASE WHEN strftime('%H', s.sale_date) >= '18' OR strftime('%H', s.sale_date) < '06' THEN s.total_amount ELSE 0 END) as night_total,
          SUM(s.payment_cash) as total_cash,
          SUM(s.payment_debit) as total_debit,
          SUM(s.payment_credit) as total_credit,
          SUM(s.total_amount) as total_amount,
          s.location_name,
          s.operator_name
        FROM sales s
        WHERE s.location_id = ?
          AND DATE(s.sale_date) >= ?
          AND DATE(s.sale_date) <= ?
          AND s.sale_type = 'deli'
          ${timeCondition}
        GROUP BY DATE(s.sale_date), s.location_name, s.operator_name
        ORDER BY DATE(s.sale_date) DESC
      `;

      this.db.all(query, [locationId, dateFrom, dateTo], (err, deliSales) => {
        if (err) {
          reject(err);
        } else {
          resolve(deliSales);
        }
      });
    });
  }

  // Get all sales (admin only)
  getAllSales() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT s.*, COUNT(si.id) as item_count_actual
        FROM sales s
        LEFT JOIN sales_items si ON s.sale_id = si.sale_id
        GROUP BY s.id
        ORDER BY s.sale_date DESC
      `;

      this.db.all(query, [], (err, sales) => {
        if (err) {
          reject(err);
        } else {
          resolve(sales);
        }
      });
    });
  }

  // Get sale details with items
  getSaleDetails(saleId) {
    return new Promise((resolve, reject) => {
      const saleQuery = `SELECT * FROM sales WHERE sale_id = ?`;
      const itemsQuery = `SELECT * FROM sales_items WHERE sale_id = ?`;

      this.db.get(saleQuery, [saleId], (err, sale) => {
        if (err) {
          reject(err);
          return;
        }

        if (!sale) {
          resolve(null);
          return;
        }

        this.db.all(itemsQuery, [saleId], (err, items) => {
          if (err) {
            reject(err);
          } else {
            resolve({ ...sale, items });
          }
        });
      });
    });
  }

  // Shift Management Methods
  startShift(shiftData) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        console.error('Database connection not available');
        reject(new Error('Database connection not available'));
        return;
      }

      // First, end any active shifts for this user at this location
      this.endActiveShifts(shiftData.user_id, shiftData.location_id).then(() => {
        // Calculate shift duration in hours (default 12)
        const durationHours = shiftData.duration_hours || 12;
        const remainingMinutes = durationHours * 60;

        // Use SQLite local time functions (same as tickets)
        const query = `
          INSERT INTO shifts (
            shift_id, user_id, location_id, operator_name, location_name,
            shift_start_time, shift_end_time, shift_duration_hours,
            remaining_time_minutes, status
          ) VALUES (
            ?, ?, ?, ?, ?,
            datetime(CURRENT_TIMESTAMP, 'localtime'),
            datetime(CURRENT_TIMESTAMP, 'localtime', '+${durationHours} hours'),
            ?, ?, 'active'
          )
        `;

        const values = [
          shiftData.shift_id,
          shiftData.user_id,
          shiftData.location_id,
          shiftData.operator_name,
          shiftData.location_name,
          durationHours,
          remainingMinutes
        ];

        const self = this; // Store reference to the database instance

        this.db.run(query, values, function(err) {
          if (err) {
            console.error('Error starting shift:', err);
            reject(err);
          } else {


            // Get the created shift to return accurate times
            const selectQuery = `SELECT * FROM shifts WHERE id = ?`;
            const shiftId = this.lastID;

            // Use the stored database reference
            setTimeout(() => {
              self.db.get(selectQuery, [shiftId], (selectErr, shift) => {
                if (selectErr) {
                  console.error('Error retrieving created shift:', selectErr);
                  reject(selectErr);
                } else {
                  resolve({
                    id: shift.id,
                    shift_id: shift.shift_id,
                    start_time: shift.shift_start_time,
                    end_time: shift.shift_end_time,
                    remaining_minutes: shift.remaining_time_minutes
                  });
                }
              });
            }, 10); // Small delay to ensure the insert is committed
          }
        });
      }).catch(reject);
    });
  }

  endActiveShifts(userId, locationId) {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE shifts
        SET status = 'ended',
            actual_end_time = datetime(CURRENT_TIMESTAMP, 'localtime'),
            updated_at = datetime(CURRENT_TIMESTAMP, 'localtime')
        WHERE user_id = ? AND location_id = ? AND status = 'active'
      `;

      this.db.run(query, [userId, locationId], function(err) {
        if (err) {
          console.error('Error ending active shifts:', err);
          reject(err);
        } else {

          resolve(this.changes);
        }
      });
    });
  }

  getCurrentShift(userId, locationId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM shifts
        WHERE user_id = ? AND location_id = ? AND status = 'active'
        ORDER BY shift_start_time DESC
        LIMIT 1
      `;

      this.db.get(query, [userId, locationId], (err, shift) => {
        if (err) {
          console.error('Error getting current shift:', err);
          reject(err);
        } else {
          // If shift exists but remaining_time_minutes is null, calculate it using day.js
          if (shift && shift.remaining_time_minutes === null) {
            // Import day.js for time calculations (same as tickets)
            const dayjs = require('dayjs');

            const now = dayjs();
            const endTime = dayjs(shift.shift_end_time);

            if (!endTime.isValid()) {
              console.error('❌ SHIFT: Invalid end time in database:', shift.shift_end_time);
              resolve(shift);
              return;
            }

            const remainingMs = endTime.diff(now);
            const remainingMinutes = Math.max(0, Math.floor(remainingMs / (1000 * 60)));



            // Update the remaining time in database
            this.updateShiftRemainingTime(shift.shift_id, remainingMinutes).then(() => {
              shift.remaining_time_minutes = remainingMinutes;
              resolve(shift);
            }).catch(() => {
              // If update fails, still return shift with calculated time
              shift.remaining_time_minutes = remainingMinutes;
              resolve(shift);
            });
          } else {
            resolve(shift);
          }
        }
      });
    });
  }

  updateShiftRemainingTime(shiftId, remainingMinutes) {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE shifts
        SET remaining_time_minutes = ?,
            updated_at = datetime(CURRENT_TIMESTAMP, 'localtime')
        WHERE shift_id = ? AND status = 'active'
      `;

      this.db.run(query, [remainingMinutes, shiftId], function(err) {
        if (err) {
          console.error('Error updating shift remaining time:', err);
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  endShift(shiftId, shiftSummary = {}) {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE shifts
        SET status = 'ended',
            actual_end_time = datetime(CURRENT_TIMESTAMP, 'localtime'),
            total_sales = ?,
            total_transactions = ?,
            total_sale_sales = ?,
            total_sale_transactions = ?,
            total_theater_sales = ?,
            total_theater_transactions = ?,
            total_deli_sales = ?,
            total_deli_transactions = ?,
            updated_at = datetime(CURRENT_TIMESTAMP, 'localtime')
        WHERE shift_id = ? AND status = 'active'
      `;

      const values = [
        shiftSummary.total_sales || 0,
        shiftSummary.total_transactions || 0,
        shiftSummary.total_sale_sales || 0,
        shiftSummary.total_sale_transactions || 0,
        shiftSummary.total_theater_sales || 0,
        shiftSummary.total_theater_transactions || 0,
        shiftSummary.total_deli_sales || 0,
        shiftSummary.total_deli_transactions || 0,
        shiftId
      ];

      this.db.run(query, values, function(err) {
        if (err) {
          console.error('Error ending shift:', err);
          reject(err);
        } else {

          resolve(this.changes > 0);
        }
      });
    });
  }

  // Update shift totals when a sale is made
  updateShiftTotalsForSale(userId, locationId, saleAmount, saleType = 'sale') {
    return new Promise((resolve, reject) => {
      console.log(`🔍 SHIFT UPDATE: Attempting to update shift totals for user ${userId}, location ${locationId}, amount $${saleAmount}, type ${saleType}`);

      // First get the current active shift
      this.getCurrentShift(userId, locationId).then(shift => {
        if (!shift) {
          console.log(`❌ SHIFT UPDATE: No active shift found for user ${userId}, location ${locationId}`);
          console.log(`   This means shift totals will NOT be updated for this sale!`);
          resolve(false);
          return;
        }

        console.log(`✅ SHIFT UPDATE: Found active shift ${shift.shift_id} for user ${userId}`);
        console.log(`   Current totals: $${shift.total_sales || 0} sales, ${shift.total_transactions || 0} transactions`);

        // Determine which columns to update based on sale type
        let salesColumn, transactionsColumn;
        switch (saleType) {
          case 'theater':
            salesColumn = 'total_theater_sales';
            transactionsColumn = 'total_theater_transactions';
            break;
          case 'deli':
            salesColumn = 'total_deli_sales';
            transactionsColumn = 'total_deli_transactions';
            break;
          default:
            salesColumn = 'total_sale_sales';
            transactionsColumn = 'total_sale_transactions';
        }

        const query = `
          UPDATE shifts
          SET total_sales = total_sales + ?,
              total_transactions = total_transactions + 1,
              ${salesColumn} = COALESCE(${salesColumn}, 0) + ?,
              ${transactionsColumn} = COALESCE(${transactionsColumn}, 0) + 1,
              updated_at = datetime(CURRENT_TIMESTAMP, 'localtime')
          WHERE shift_id = ? AND status = 'active'
        `;

        this.db.run(query, [saleAmount, saleAmount, shift.shift_id], function(err) {
          if (err) {
            console.error('❌ SHIFT UPDATE: Error updating shift totals:', err);
            reject(err);
          } else {
            console.log(`✅ SHIFT UPDATE: Successfully updated shift ${shift.shift_id}`);
            console.log(`   Added: $${saleAmount} (${saleType}), Rows affected: ${this.changes}`);
            resolve(this.changes > 0);
          }
        });
      }).catch(reject);
    });
  }

  getShiftHistory(locationId, limit = 50) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM shifts
        WHERE location_id = ?
        ORDER BY shift_start_time DESC
        LIMIT ?
      `;

      this.db.all(query, [locationId, limit], (err, shifts) => {
        if (err) {
          console.error('Error getting shift history:', err);
          reject(err);
        } else {
          resolve(shifts);
        }
      });
    });
  }

  // Recalculate shift totals from sales history
  recalculateShiftTotals(shiftId) {
    return new Promise((resolve, reject) => {
      // Get shift details
      const shiftQuery = `
        SELECT shift_id, user_id, location_id, shift_start_time,
               COALESCE(actual_end_time, shift_end_time) as end_time
        FROM shifts
        WHERE shift_id = ?
      `;

      this.db.get(shiftQuery, [shiftId], (err, shift) => {
        if (err) {
          reject(err);
          return;
        }

        if (!shift) {
          reject(new Error('Shift not found'));
          return;
        }

        // Calculate totals from sales during this shift
        const salesQuery = `
          SELECT
            sale_type,
            COUNT(*) as transaction_count,
            SUM(total_amount) as total_sales
          FROM sales
          WHERE user_id = ?
            AND location_id = ?
            AND sale_date >= ?
            AND sale_date <= ?
            AND status = 'completed'
          GROUP BY sale_type
        `;

        this.db.all(salesQuery, [
          shift.user_id,
          shift.location_id,
          shift.shift_start_time,
          shift.end_time
        ], (err, salesData) => {
          if (err) {
            reject(err);
            return;
          }

          // Initialize totals
          let totals = {
            total_sales: 0,
            total_transactions: 0,
            total_sale_sales: 0,
            total_sale_transactions: 0,
            total_theater_sales: 0,
            total_theater_transactions: 0,
            total_deli_sales: 0,
            total_deli_transactions: 0
          };

          // Process sales data
          salesData.forEach(row => {
            const amount = parseFloat(row.total_sales) || 0;
            const count = parseInt(row.transaction_count) || 0;

            totals.total_sales += amount;
            totals.total_transactions += count;

            switch (row.sale_type) {
              case 'theater':
                totals.total_theater_sales += amount;
                totals.total_theater_transactions += count;
                break;
              case 'deli':
                totals.total_deli_sales += amount;
                totals.total_deli_transactions += count;
                break;
              default:
                totals.total_sale_sales += amount;
                totals.total_sale_transactions += count;
            }
          });

          // Update shift with calculated totals
          const updateQuery = `
            UPDATE shifts
            SET total_sales = ?,
                total_transactions = ?,
                total_sale_sales = ?,
                total_sale_transactions = ?,
                total_theater_sales = ?,
                total_theater_transactions = ?,
                total_deli_sales = ?,
                total_deli_transactions = ?,
                updated_at = datetime(CURRENT_TIMESTAMP, 'localtime')
            WHERE shift_id = ?
          `;

          this.db.run(updateQuery, [
            totals.total_sales,
            totals.total_transactions,
            totals.total_sale_sales,
            totals.total_sale_transactions,
            totals.total_theater_sales,
            totals.total_theater_transactions,
            totals.total_deli_sales,
            totals.total_deli_transactions,
            shiftId
          ], function(err) {
            if (err) {
              reject(err);
            } else {
              console.log(`✅ Shift ${shiftId} totals recalculated:`, totals);
              resolve(totals);
            }
          });
        });
      });
    });
  }

  // Recalculate all shift totals from sales history
  recalculateAllShiftTotals() {
    return new Promise((resolve, reject) => {
      const query = `SELECT shift_id FROM shifts ORDER BY shift_start_time DESC`;

      this.db.all(query, [], (err, shifts) => {
        if (err) {
          reject(err);
          return;
        }

        if (shifts.length === 0) {
          resolve([]);
          return;
        }

        const promises = shifts.map(shift => this.recalculateShiftTotals(shift.shift_id));

        Promise.all(promises).then(results => {
          console.log(`✅ Recalculated totals for ${results.length} shifts`);
          resolve(results);
        }).catch(reject);
      });
    });
  }

  expireOldShifts() {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE shifts
        SET status = 'expired',
            updated_at = datetime(CURRENT_TIMESTAMP, 'localtime')
        WHERE status = 'active'
          AND datetime(shift_end_time) < datetime(CURRENT_TIMESTAMP, 'localtime')
      `;

      this.db.run(query, [], function(err) {
        if (err) {
          console.error('Error expiring old shifts:', err);
          reject(err);
        } else {

          resolve(this.changes);
        }
      });
    });
  }

  // Shift Report Methods
  getShiftReport(shiftId) {
    return new Promise((resolve, reject) => {
      const shiftQuery = `
        SELECT s.*, u.username as operator_name, l.name as location_name
        FROM shifts s
        LEFT JOIN users u ON s.user_id = u.id
        LEFT JOIN locations l ON s.location_id = l.id
        WHERE s.id = ? OR s.shift_id = ?
      `;

      this.db.get(shiftQuery, [shiftId, shiftId], (err, shift) => {
        if (err) {
          reject(err);
          return;
        }

        if (!shift) {
          resolve(null);
          return;
        }

        // Get transactions for this shift
        const transactionsQuery = `
          SELECT s.*, GROUP_CONCAT(si.product_name || ' x' || si.quantity) as items
          FROM sales s
          LEFT JOIN sale_items si ON s.id = si.sale_id
          WHERE s.sale_date >= ? AND s.sale_date <= ? AND s.location_id = ?
          GROUP BY s.id
          ORDER BY s.sale_date DESC
        `;

        this.db.all(transactionsQuery, [
          shift.shift_start_time,
          shift.actual_end_time || shift.shift_end_time,
          shift.location_id
        ], (err, transactions) => {
          if (err) {
            reject(err);
            return;
          }

          // Calculate shift statistics
          const totalSales = transactions.reduce((sum, t) => sum + parseFloat(t.total_amount || 0), 0);
          const transactionCount = transactions.length;
          const averageTransaction = transactionCount > 0 ? totalSales / transactionCount : 0;

          // Calculate shift duration
          const startTime = new Date(shift.shift_start_time);
          const endTime = new Date(shift.actual_end_time || shift.shift_end_time);
          const durationMs = endTime - startTime;
          const durationHours = Math.floor(durationMs / (1000 * 60 * 60));
          const durationMinutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
          const duration = `${durationHours}h ${durationMinutes}m`;

          resolve({
            shift: shift,
            operator_name: shift.operator_name,
            location: shift.location_name,
            shift_start: shift.shift_start_time,
            shift_end: shift.actual_end_time || shift.shift_end_time,
            duration: duration,
            totalSales: totalSales,
            transactionCount: transactionCount,
            averageTransaction: averageTransaction,
            transactions: transactions.map(t => ({
              id: t.id,
              timestamp: t.sale_date,
              total: t.total_amount,
              payment_method: t.payment_method,
              items_count: t.items ? t.items.split(',').length : 0,
              items: t.items
            }))
          });
        });
      });
    });
  }

  getCurrentShiftReport(userId, locationId) {
    return new Promise((resolve, reject) => {
      // Get current active shift
      const shiftQuery = `
        SELECT * FROM shifts
        WHERE user_id = ? AND location_id = ? AND status = 'active'
        ORDER BY shift_start_time DESC
        LIMIT 1
      `;

      this.db.get(shiftQuery, [userId, locationId], (err, shift) => {
        if (err) {
          reject(err);
          return;
        }

        if (!shift) {
          resolve(null);
          return;
        }

        // Use the getShiftReport method to get full details
        this.getShiftReport(shift.id)
          .then(resolve)
          .catch(reject);
      });
    });
  }

  // Email Configuration Methods
  getEmailConfiguration() {
    return new Promise((resolve, reject) => {
      const query = 'SELECT * FROM email_configuration ORDER BY id DESC LIMIT 1';

      this.db.get(query, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  saveEmailConfiguration(config) {
    return new Promise((resolve, reject) => {
      // First check if configuration exists
      this.db.get('SELECT id FROM email_configuration LIMIT 1', (err, row) => {
        if (err) {
          reject(err);
          return;
        }

        const now = new Date().toISOString();

        if (row) {
          // Update existing configuration
          const updateQuery = `
            UPDATE email_configuration
            SET resend_api_key = ?,
                business_owner_email = ?,
                sender_name = ?,
                auto_send_daily_reports = ?,
                daily_report_time = ?,
                updated_at = ?
            WHERE id = ?
          `;

          this.db.run(updateQuery, [
            config.resend_api_key || null,
            config.business_owner_email || null,
            config.sender_name || 'Rainbow Station POS',
            config.auto_send_daily_reports || 0,
            config.daily_report_time || '18:00',
            now,
            row.id
          ], function(err) {
            if (err) {
              reject(err);
            } else {
              resolve({ id: row.id, changes: this.changes });
            }
          });
        } else {
          // Insert new configuration
          const insertQuery = `
            INSERT INTO email_configuration
            (resend_api_key, business_owner_email, sender_name, auto_send_daily_reports, daily_report_time, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
          `;

          this.db.run(insertQuery, [
            config.resend_api_key || null,
            config.business_owner_email || null,
            config.sender_name || 'Rainbow Station POS',
            config.auto_send_daily_reports || 0,
            config.daily_report_time || '18:00',
            now,
            now
          ], function(err) {
            if (err) {
              reject(err);
            } else {
              resolve({ id: this.lastID, changes: this.changes });
            }
          });
        }
      });
    });
  }

  // Generate and store daily sales report data
  async generateDailySalesReportData(locationId, reportDate) {
    return new Promise((resolve, reject) => {
      // Get aggregated sales data for the date
      const salesQuery = `
        SELECT
          COUNT(*) as total_transactions,
          SUM(total_amount) as total_sales,
          AVG(total_amount) as average_transaction,
          SUM(item_count) as products_sold,
          SUM(payment_cash) as total_cash,
          SUM(payment_debit) as total_debit,
          SUM(payment_credit) as total_credit,
          SUM(subtotal) as subtotal,
          SUM(tax_amount) as tax_amount,

          -- Day shift data (6 AM - 6 PM)
          SUM(CASE WHEN strftime('%H', sale_date) >= '06' AND strftime('%H', sale_date) < '18'
              THEN payment_cash ELSE 0 END) as day_cash,
          SUM(CASE WHEN strftime('%H', sale_date) >= '06' AND strftime('%H', sale_date) < '18'
              THEN payment_debit ELSE 0 END) as day_debit,
          SUM(CASE WHEN strftime('%H', sale_date) >= '06' AND strftime('%H', sale_date) < '18'
              THEN payment_credit ELSE 0 END) as day_credit,
          SUM(CASE WHEN strftime('%H', sale_date) >= '06' AND strftime('%H', sale_date) < '18'
              THEN total_amount ELSE 0 END) as day_sales,
          COUNT(CASE WHEN strftime('%H', sale_date) >= '06' AND strftime('%H', sale_date) < '18'
                THEN 1 END) as day_transactions,

          -- Night shift data (6 PM - 6 AM)
          SUM(CASE WHEN strftime('%H', sale_date) >= '18' OR strftime('%H', sale_date) < '06'
              THEN payment_cash ELSE 0 END) as night_cash,
          SUM(CASE WHEN strftime('%H', sale_date) >= '18' OR strftime('%H', sale_date) < '06'
              THEN payment_debit ELSE 0 END) as night_debit,
          SUM(CASE WHEN strftime('%H', sale_date) >= '18' OR strftime('%H', sale_date) < '06'
              THEN payment_credit ELSE 0 END) as night_credit,
          SUM(CASE WHEN strftime('%H', sale_date) >= '18' OR strftime('%H', sale_date) < '06'
              THEN total_amount ELSE 0 END) as night_sales,
          COUNT(CASE WHEN strftime('%H', sale_date) >= '18' OR strftime('%H', sale_date) < '06'
                THEN 1 END) as night_transactions,

          -- POS sales (sale_type = 'sale')
          SUM(CASE WHEN sale_type = 'sale' THEN total_amount ELSE 0 END) as pos_sales,
          COUNT(CASE WHEN sale_type = 'sale' THEN 1 END) as pos_transactions,

          location_name,
          operator_name
        FROM sales
        WHERE location_id = ? AND DATE(sale_date) = ? AND status = 'completed'
        GROUP BY location_name, operator_name
      `;

      this.db.all(salesQuery, [locationId, reportDate], (err, salesData) => {
        if (err) {
          reject(err);
          return;
        }

        // Get theater sales data
        const theaterQuery = `
          SELECT
            COUNT(*) as theater_transactions,
            SUM(total_amount) as theater_sales
          FROM tickets
          WHERE location_id = ? AND DATE(issued_at) = ? AND status = 'active'
        `;

        this.db.get(theaterQuery, [locationId, reportDate], (err, theaterData) => {
          if (err) {
            reject(err);
            return;
          }

          // Get deli sales data (assuming deli sales are marked with sale_type = 'deli')
          const deliQuery = `
            SELECT
              COUNT(*) as deli_transactions,
              SUM(total_amount) as deli_sales
            FROM sales
            WHERE location_id = ? AND DATE(sale_date) = ? AND sale_type = 'deli' AND status = 'completed'
          `;

          this.db.get(deliQuery, [locationId, reportDate], (err, deliData) => {
            if (err) {
              reject(err);
              return;
            }

            // Get location name
            const locationQuery = `SELECT location FROM locations WHERE id = ?`;
            this.db.get(locationQuery, [locationId], (err, location) => {
              if (err) {
                reject(err);
                return;
              }

              // Process and combine all data
              // Note: Theater and deli sales are separate systems, so we add them to POS sales
              // But we need to ensure no double-counting occurs

              let processedData = [];

              if (salesData.length > 0) {
                // Process each operator's sales data
                processedData = salesData.map(sale => ({
                  report_date: reportDate,
                  location_id: locationId,
                  location_name: location?.location || 'Unknown Location',
                  operator_name: sale.operator_name || 'Unknown Operator',

                  // Core sales data (from sales table only)
                  total_sales: (sale.total_sales || 0) + (theaterData?.theater_sales || 0) + (deliData?.deli_sales || 0),
                  total_transactions: (sale.total_transactions || 0) + (theaterData?.theater_transactions || 0) + (deliData?.deli_transactions || 0),
                  average_transaction: sale.average_transaction || 0,
                  products_sold: sale.products_sold || 0,

                  // Payment method breakdown (POS sales only)
                  total_cash: sale.total_cash || 0,
                  total_debit: sale.total_debit || 0,
                  total_credit: sale.total_credit || 0,

                  // Day/night shift breakdown (POS sales only)
                  day_cash: sale.day_cash || 0,
                  day_debit: sale.day_debit || 0,
                  day_credit: sale.day_credit || 0,
                  day_sales: sale.day_sales || 0,
                  day_transactions: sale.day_transactions || 0,
                  night_cash: sale.night_cash || 0,
                  night_debit: sale.night_debit || 0,
                  night_credit: sale.night_credit || 0,
                  night_sales: sale.night_sales || 0,
                  night_transactions: sale.night_transactions || 0,

                  // Sale type breakdown
                  pos_sales: sale.pos_sales || 0,
                  pos_transactions: sale.pos_transactions || 0,
                  theater_sales: theaterData?.theater_sales || 0,
                  theater_transactions: theaterData?.theater_transactions || 0,
                  deli_sales: deliData?.deli_sales || 0,
                  deli_transactions: deliData?.deli_transactions || 0,

                  // Tax information
                  subtotal: sale.subtotal || 0,
                  tax_amount: sale.tax_amount || 0,
                  tax_rate: sale.tax_amount && sale.subtotal ? (sale.tax_amount / sale.subtotal) * 100 : 0
                }));
              } else {
                // No POS sales, but might have theater/deli sales
                if ((theaterData?.theater_sales || 0) > 0 || (deliData?.deli_sales || 0) > 0) {
                  processedData = [{
                    report_date: reportDate,
                    location_id: locationId,
                    location_name: location?.location || 'Unknown Location',
                    operator_name: 'System', // Default operator for non-POS sales

                    total_sales: (theaterData?.theater_sales || 0) + (deliData?.deli_sales || 0),
                    total_transactions: (theaterData?.theater_transactions || 0) + (deliData?.deli_transactions || 0),
                    average_transaction: 0,
                    products_sold: 0,

                    total_cash: 0,
                    total_debit: 0,
                    total_credit: 0,
                    day_cash: 0,
                    day_debit: 0,
                    day_credit: 0,
                    day_sales: 0,
                    day_transactions: 0,
                    night_cash: 0,
                    night_debit: 0,
                    night_credit: 0,
                    night_sales: 0,
                    night_transactions: 0,

                    pos_sales: 0,
                    pos_transactions: 0,
                    theater_sales: theaterData?.theater_sales || 0,
                    theater_transactions: theaterData?.theater_transactions || 0,
                    deli_sales: deliData?.deli_sales || 0,
                    deli_transactions: deliData?.deli_transactions || 0,

                    subtotal: 0,
                    tax_amount: 0,
                    tax_rate: 0
                  }];
                }
              }

              // Store the aggregated data
              if (processedData.length > 0) {
                const promises = processedData.map(data => this.storeDailySalesReport(data));
                Promise.all(promises)
                  .then(results => resolve(results))
                  .catch(error => reject(error));
              } else {
                resolve([]); // No data to store
              }
            });
          });
        });
      });
    });
  }

  // Store daily sales report data with proper upsert logic
  storeDailySalesReport(reportData) {
    return new Promise((resolve, reject) => {
      // First check if record exists
      const checkQuery = `
        SELECT id FROM daily_sales_reports
        WHERE report_date = ? AND location_id = ? AND operator_name = ?
      `;

      this.db.get(checkQuery, [
        reportData.report_date,
        reportData.location_id,
        reportData.operator_name
      ], (err, existingRecord) => {
        if (err) {
          reject(err);
          return;
        }

        const now = new Date().toISOString();

        if (existingRecord) {
          // Update existing record
          const updateQuery = `
            UPDATE daily_sales_reports SET
              location_name = ?,
              total_sales = ?,
              total_transactions = ?,
              average_transaction = ?,
              products_sold = ?,
              total_cash = ?,
              total_debit = ?,
              total_credit = ?,
              day_cash = ?,
              day_debit = ?,
              day_credit = ?,
              day_sales = ?,
              day_transactions = ?,
              night_cash = ?,
              night_debit = ?,
              night_credit = ?,
              night_sales = ?,
              night_transactions = ?,
              pos_sales = ?,
              pos_transactions = ?,
              theater_sales = ?,
              theater_transactions = ?,
              deli_sales = ?,
              deli_transactions = ?,
              subtotal = ?,
              tax_amount = ?,
              tax_rate = ?,
              updated_at = ?
            WHERE id = ?
          `;

          this.db.run(updateQuery, [
            reportData.location_name,
            reportData.total_sales,
            reportData.total_transactions,
            reportData.average_transaction,
            reportData.products_sold,
            reportData.total_cash,
            reportData.total_debit,
            reportData.total_credit,
            reportData.day_cash,
            reportData.day_debit,
            reportData.day_credit,
            reportData.day_sales,
            reportData.day_transactions,
            reportData.night_cash,
            reportData.night_debit,
            reportData.night_credit,
            reportData.night_sales,
            reportData.night_transactions,
            reportData.pos_sales,
            reportData.pos_transactions,
            reportData.theater_sales,
            reportData.theater_transactions,
            reportData.deli_sales,
            reportData.deli_transactions,
            reportData.subtotal,
            reportData.tax_amount,
            reportData.tax_rate,
            now,
            existingRecord.id
          ], function(err) {
            if (err) {
              reject(err);
            } else {
              resolve({ id: existingRecord.id, changes: this.changes, action: 'updated' });
            }
          });
        } else {
          // Insert new record
          const insertQuery = `
            INSERT INTO daily_sales_reports (
              report_date, location_id, location_name, operator_name,
              total_sales, total_transactions, average_transaction, products_sold,
              total_cash, total_debit, total_credit,
              day_cash, day_debit, day_credit, day_sales, day_transactions,
              night_cash, night_debit, night_credit, night_sales, night_transactions,
              pos_sales, pos_transactions, theater_sales, theater_transactions,
              deli_sales, deli_transactions, subtotal, tax_amount, tax_rate,
              generated_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `;

          this.db.run(insertQuery, [
            reportData.report_date,
            reportData.location_id,
            reportData.location_name,
            reportData.operator_name,
            reportData.total_sales,
            reportData.total_transactions,
            reportData.average_transaction,
            reportData.products_sold,
            reportData.total_cash,
            reportData.total_debit,
            reportData.total_credit,
            reportData.day_cash,
            reportData.day_debit,
            reportData.day_credit,
            reportData.day_sales,
            reportData.day_transactions,
            reportData.night_cash,
            reportData.night_debit,
            reportData.night_credit,
            reportData.night_sales,
            reportData.night_transactions,
            reportData.pos_sales,
            reportData.pos_transactions,
            reportData.theater_sales,
            reportData.theater_transactions,
            reportData.deli_sales,
            reportData.deli_transactions,
            reportData.subtotal,
            reportData.tax_amount,
            reportData.tax_rate,
            now,
            now
          ], function(err) {
            if (err) {
              reject(err);
            } else {
              resolve({ id: this.lastID, changes: this.changes, action: 'inserted' });
            }
          });
        }
      });
    });
  }

  // Generate and store shift sales report data
  async generateShiftSalesReportData(shiftId) {
    return new Promise((resolve, reject) => {
      // Get shift information
      const shiftQuery = `
        SELECT s.*, l.location as location_name, u.name as operator_name
        FROM shifts s
        LEFT JOIN locations l ON s.location_id = l.id
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.shift_id = ?
      `;

      this.db.get(shiftQuery, [shiftId], (err, shift) => {
        if (err) {
          reject(err);
          return;
        }

        if (!shift) {
          resolve(null);
          return;
        }

        // Calculate shift duration
        const startTime = new Date(shift.shift_start_time);
        const endTime = new Date(shift.actual_end_time || shift.shift_end_time);
        const durationHours = (endTime - startTime) / (1000 * 60 * 60);

        // Get sales data for this shift
        const salesQuery = `
          SELECT
            COUNT(*) as total_transactions,
            SUM(total_amount) as total_sales,
            AVG(total_amount) as average_transaction,
            SUM(item_count) as products_sold,
            SUM(payment_cash) as total_cash,
            SUM(payment_debit) as total_debit,
            SUM(payment_credit) as total_credit,
            SUM(CASE WHEN sale_type = 'sale' THEN total_amount ELSE 0 END) as pos_sales,
            SUM(CASE WHEN sale_type = 'deli' THEN total_amount ELSE 0 END) as deli_sales
          FROM sales
          WHERE user_id = ? AND location_id = ?
            AND sale_date >= ? AND sale_date <= ?
            AND status = 'completed'
        `;

        this.db.get(salesQuery, [
          shift.user_id,
          shift.location_id,
          shift.shift_start_time,
          shift.actual_end_time || shift.shift_end_time
        ], (err, salesData) => {
          if (err) {
            reject(err);
            return;
          }

          // Get theater sales for this shift
          const theaterQuery = `
            SELECT
              COUNT(*) as theater_transactions,
              SUM(total_amount) as theater_sales
            FROM tickets
            WHERE user_id = ? AND location_id = ?
              AND issued_at >= ? AND issued_at <= ?
              AND status = 'active'
          `;

          this.db.get(theaterQuery, [
            shift.user_id,
            shift.location_id,
            shift.shift_start_time,
            shift.actual_end_time || shift.shift_end_time
          ], (err, theaterData) => {
            if (err) {
              reject(err);
              return;
            }

            // Determine shift type based on start time
            const startHour = startTime.getHours();
            let shiftType = 'full';
            if (startHour >= 6 && startHour < 18) {
              shiftType = 'day';
            } else if (startHour >= 18 || startHour < 6) {
              shiftType = 'night';
            }

            const totalSales = (salesData?.total_sales || 0) + (theaterData?.theater_sales || 0);
            const totalTransactions = (salesData?.total_transactions || 0) + (theaterData?.theater_transactions || 0);

            const reportData = {
              shift_id: shift.shift_id,
              shift_date: shift.shift_start_time.split(' ')[0], // Extract date part
              location_id: shift.location_id,
              location_name: shift.location_name || 'Unknown Location',
              user_id: shift.user_id,
              operator_id: shift.user_id,
              operator_name: shift.operator_name || 'Unknown Operator',
              shift_start_time: shift.shift_start_time,
              shift_end_time: shift.actual_end_time || shift.shift_end_time,
              shift_duration_hours: durationHours,
              shift_type: shiftType,
              total_sales: totalSales,
              total_transactions: totalTransactions,
              average_transaction: totalTransactions > 0 ? totalSales / totalTransactions : 0,
              products_sold: salesData?.products_sold || 0,
              total_cash: salesData?.total_cash || 0,
              total_debit: salesData?.total_debit || 0,
              total_credit: salesData?.total_credit || 0,
              pos_sales: salesData?.pos_sales || 0,
              theater_sales: theaterData?.theater_sales || 0,
              deli_sales: salesData?.deli_sales || 0,
              sales_per_hour: durationHours > 0 ? totalSales / durationHours : 0,
              transactions_per_hour: durationHours > 0 ? totalTransactions / durationHours : 0,
              shift_status: shift.status || 'completed'
            };

            // Store the shift report data
            this.storeShiftSalesReport(reportData)
              .then(result => resolve(result))
              .catch(error => reject(error));
          });
        });
      });
    });
  }

  // Store shift sales report data
  storeShiftSalesReport(reportData) {
    return new Promise((resolve, reject) => {
      const insertQuery = `
        INSERT OR REPLACE INTO shift_sales_reports (
          shift_id, shift_date, location_id, location_name, user_id, operator_id, operator_name,
          shift_start_time, shift_end_time, shift_duration_hours, shift_type,
          total_sales, total_transactions, average_transaction, products_sold,
          total_cash, total_debit, total_credit,
          pos_sales, theater_sales, deli_sales,
          sales_per_hour, transactions_per_hour, shift_status,
          created_at, updated_at, synced_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const now = new Date().toISOString();
      this.db.run(insertQuery, [
        reportData.shift_id,
        reportData.shift_date,
        reportData.location_id,
        reportData.location_name,
        reportData.user_id,
        reportData.operator_id,
        reportData.operator_name,
        reportData.shift_start_time,
        reportData.shift_end_time,
        reportData.shift_duration_hours,
        reportData.shift_type,
        reportData.total_sales,
        reportData.total_transactions,
        reportData.average_transaction,
        reportData.products_sold,
        reportData.total_cash,
        reportData.total_debit,
        reportData.total_credit,
        reportData.pos_sales,
        reportData.theater_sales,
        reportData.deli_sales,
        reportData.sales_per_hour,
        reportData.transactions_per_hour,
        reportData.shift_status,
        now,
        now,
        now
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  // Database migrations for sales reports tables
  runSalesReportsMigrations() {
    console.log('🔄 Running sales reports database migrations...');

    // For simplicity, let's drop and recreate the sales reports tables with correct schema
    // This is safe since these are aggregated tables that can be regenerated
    this.recreateSalesReportsTables();
  }

  // Recreate sales reports tables with correct schema
  recreateSalesReportsTables() {
    console.log('📊 Recreating sales reports tables with correct schema...');

    // Drop existing tables
    this.db.run("DROP TABLE IF EXISTS daily_sales_report_details", (err) => {
      if (err) console.error('Error dropping daily_sales_report_details:', err);
    });

    this.db.run("DROP TABLE IF EXISTS shift_sales_report_details", (err) => {
      if (err) console.error('Error dropping shift_sales_report_details:', err);
    });

    this.db.run("DROP TABLE IF EXISTS daily_sales_reports", (err) => {
      if (err) console.error('Error dropping daily_sales_reports:', err);
    });

    this.db.run("DROP TABLE IF EXISTS shift_sales_reports", (err) => {
      if (err) console.error('Error dropping shift_sales_reports:', err);
    });

    // Wait a moment then recreate with correct schema
    setTimeout(() => {
      const createDailySalesReportsTable = `
        CREATE TABLE IF NOT EXISTS daily_sales_reports (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          report_date DATE NOT NULL,
          location_id INTEGER NOT NULL,
          location_name TEXT NOT NULL,
          operator_name TEXT,

          -- Sales Summary Data
          total_sales DECIMAL(10,2) DEFAULT 0,
          total_transactions INTEGER DEFAULT 0,
          average_transaction DECIMAL(10,2) DEFAULT 0,
          products_sold INTEGER DEFAULT 0,

          -- Payment Method Breakdown
          total_cash DECIMAL(10,2) DEFAULT 0,
          total_debit DECIMAL(10,2) DEFAULT 0,
          total_credit DECIMAL(10,2) DEFAULT 0,

          -- Day Shift Data (6 AM - 6 PM)
          day_cash DECIMAL(10,2) DEFAULT 0,
          day_debit DECIMAL(10,2) DEFAULT 0,
          day_credit DECIMAL(10,2) DEFAULT 0,
          day_sales DECIMAL(10,2) DEFAULT 0,
          day_transactions INTEGER DEFAULT 0,

          -- Night Shift Data (6 PM - 6 AM)
          night_cash DECIMAL(10,2) DEFAULT 0,
          night_debit DECIMAL(10,2) DEFAULT 0,
          night_credit DECIMAL(10,2) DEFAULT 0,
          night_sales DECIMAL(10,2) DEFAULT 0,
          night_transactions INTEGER DEFAULT 0,

          -- Sale Type Breakdown
          pos_sales DECIMAL(10,2) DEFAULT 0,
          pos_transactions INTEGER DEFAULT 0,
          theater_sales DECIMAL(10,2) DEFAULT 0,
          theater_transactions INTEGER DEFAULT 0,
          deli_sales DECIMAL(10,2) DEFAULT 0,
          deli_transactions INTEGER DEFAULT 0,

          -- Tax Information
          subtotal DECIMAL(10,2) DEFAULT 0,
          tax_amount DECIMAL(10,2) DEFAULT 0,
          tax_rate DECIMAL(5,3) DEFAULT 0,

          -- Metadata
          generated_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
          updated_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),

          -- Constraints
          UNIQUE(report_date, location_id, operator_name),
          FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE CASCADE
        )
      `;

      const createShiftSalesReportsTable = `
        CREATE TABLE IF NOT EXISTS shift_sales_reports (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          shift_id TEXT NOT NULL UNIQUE,
          shift_date DATE NOT NULL,
          location_id INTEGER NOT NULL,
          location_name TEXT NOT NULL,
          user_id INTEGER NOT NULL,
          operator_id INTEGER NOT NULL,
          operator_name TEXT NOT NULL,

          -- Shift Timing
          shift_start_time DATETIME,
          shift_end_time DATETIME,
          shift_duration_hours DECIMAL(4,2) DEFAULT 0,
          shift_type TEXT, -- 'day', 'night', 'full'

          -- Sales Summary Data
          total_sales DECIMAL(10,2) DEFAULT 0,
          total_transactions INTEGER DEFAULT 0,
          average_transaction DECIMAL(10,2) DEFAULT 0,
          products_sold INTEGER DEFAULT 0,

          -- Payment Method Breakdown
          total_cash DECIMAL(10,2) DEFAULT 0,
          total_debit DECIMAL(10,2) DEFAULT 0,
          total_credit DECIMAL(10,2) DEFAULT 0,

          -- Sale Type Breakdown
          pos_sales DECIMAL(10,2) DEFAULT 0,
          theater_sales DECIMAL(10,2) DEFAULT 0,
          deli_sales DECIMAL(10,2) DEFAULT 0,

          -- Performance Metrics
          sales_per_hour DECIMAL(10,2) DEFAULT 0,
          transactions_per_hour DECIMAL(6,2) DEFAULT 0,

          -- Shift Status
          shift_status TEXT DEFAULT 'active', -- 'active', 'completed', 'extended'

          -- Metadata
          created_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
          updated_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),
          synced_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),

          FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE CASCADE,
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
          FOREIGN KEY (operator_id) REFERENCES users (id) ON DELETE CASCADE
        )
      `;

      const createDailySalesReportDetailsTable = `
        CREATE TABLE IF NOT EXISTS daily_sales_report_details (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          daily_report_id INTEGER NOT NULL,
          sale_id TEXT NOT NULL,
          transaction_time DATETIME NOT NULL,
          operator_name TEXT NOT NULL,
          sale_type TEXT NOT NULL,
          payment_method TEXT NOT NULL,
          shift_type TEXT,
          subtotal DECIMAL(10,2) NOT NULL,
          tax_amount DECIMAL(10,2) DEFAULT 0,
          total_amount DECIMAL(10,2) NOT NULL,
          item_count INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),

          FOREIGN KEY (daily_report_id) REFERENCES daily_sales_reports (id) ON DELETE CASCADE
        )
      `;

      const createShiftSalesReportDetailsTable = `
        CREATE TABLE IF NOT EXISTS shift_sales_report_details (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          shift_report_id INTEGER NOT NULL,
          sale_id TEXT NOT NULL,
          transaction_time DATETIME NOT NULL,
          sale_type TEXT NOT NULL,
          payment_method TEXT NOT NULL,
          subtotal DECIMAL(10,2) NOT NULL,
          tax_amount DECIMAL(10,2) DEFAULT 0,
          total_amount DECIMAL(10,2) NOT NULL,
          item_count INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT (datetime(CURRENT_TIMESTAMP, 'localtime')),

          FOREIGN KEY (shift_report_id) REFERENCES shift_sales_reports (id) ON DELETE CASCADE
        )
      `;

      // Create tables with correct schema
      this.db.run(createDailySalesReportsTable, (err) => {
        if (err) {
          console.error('Error recreating daily_sales_reports table:', err);
        } else {
          console.log('✅ Daily sales reports table recreated successfully');
        }
      });

      this.db.run(createShiftSalesReportsTable, (err) => {
        if (err) {
          console.error('Error recreating shift_sales_reports table:', err);
        } else {
          console.log('✅ Shift sales reports table recreated successfully');
        }
      });

      this.db.run(createDailySalesReportDetailsTable, (err) => {
        if (err) {
          console.error('Error recreating daily_sales_report_details table:', err);
        } else {
          console.log('✅ Daily sales report details table recreated successfully');
        }
      });

      this.db.run(createShiftSalesReportDetailsTable, (err) => {
        if (err) {
          console.error('Error recreating shift_sales_report_details table:', err);
        } else {
          console.log('✅ Shift sales report details table recreated successfully');
        }
      });
    }, 500);
  }

  // Clear all sales reports data (for testing/debugging)
  clearSalesReportsData() {
    return new Promise((resolve, reject) => {
      this.db.run("DELETE FROM daily_sales_report_details", (err) => {
        if (err) console.error('Error clearing daily_sales_report_details:', err);
      });

      this.db.run("DELETE FROM shift_sales_report_details", (err) => {
        if (err) console.error('Error clearing shift_sales_report_details:', err);
      });

      this.db.run("DELETE FROM daily_sales_reports", (err) => {
        if (err) console.error('Error clearing daily_sales_reports:', err);
      });

      this.db.run("DELETE FROM shift_sales_reports", (err) => {
        if (err) {
          console.error('Error clearing shift_sales_reports:', err);
          reject(err);
        } else {
          console.log('✅ All sales reports data cleared');
          resolve();
        }
      });
    });
  }

  // Regenerate all sales reports data for a specific date range
  async regenerateAllSalesReports(locationId, daysBack = 7) {
    try {
      console.log(`🔄 Regenerating sales reports for location ${locationId}, last ${daysBack} days...`);

      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - daysBack);

      const results = [];

      for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
        const dateStr = d.toISOString().split('T')[0];
        try {
          const result = await this.generateDailySalesReportData(locationId, dateStr);
          if (result && result.length > 0) {
            results.push({ date: dateStr, success: true, count: result.length });
          }
        } catch (error) {
          results.push({ date: dateStr, success: false, error: error.message });
        }
      }

      console.log('✅ Sales reports regeneration completed');
      return results;
    } catch (error) {
      console.error('❌ Error regenerating sales reports:', error);
      throw error;
    }
  }

  close() {
    if (this.db) {
      this.db.close();
    }
  }
}

module.exports = Database;
